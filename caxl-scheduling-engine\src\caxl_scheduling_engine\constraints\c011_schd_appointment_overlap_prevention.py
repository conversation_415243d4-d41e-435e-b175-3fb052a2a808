"""
Appointment Overlap Prevention Constraint (C011)

This constraint prevents providers from being double-booked for the same time slot.
This is a HARD constraint that must be satisfied for valid assignments.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint, Joiners

from caxl_scheduling_engine.model.planning_models import TimeSlotAssignment


def no_double_booking(constraint_factory: ConstraintFactory) -> Constraint:
    """Provider cannot be double-booked for the same time slot."""
    return (constraint_factory
            .for_each(TimeSlotAssignment)
            .join(TimeSlotAssignment,
                  Joiners.equal(lambda assignment: assignment.time_slot),
                  Joiners.equal(lambda assignment: assignment.scheduled_appointment.provider))
            .filter(lambda assignment1, assignment2: assignment1.id != assignment2.id)
            .penalize(HardSoftScore.ONE_HARD, lambda assignment1, assignment2: 1)
            .as_constraint("No double booking")) 