"""
Geographic Clustering Optimization Constraint (C006)

This constraint maintains capacity thresholds for service types per date.
This is a SOFT constraint for optimization preferences.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint, Joiners

from caxl_scheduling_engine.model.planning_models import AppointmentAssignment


def capacity_thresholds(constraint_factory: ConstraintFactory) -> Constraint:
    """Maintain capacity thresholds for service types per date."""
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .join(AppointmentAssignment,
                  Joiners.equal(lambda assignment: assignment.assigned_date),
                  Joiners.equal(lambda assignment: assignment.appointment_data.required_skills))
            .group_by(lambda assignment1, assignment2: assignment1.assigned_date,
                     lambda assignment1, assignment2: 1)
            .filter(lambda assigned_date, count: count > 3)  # Max 3 appointments per date per service type
            .penalize(HardSoftScore.ONE_SOFT, lambda assigned_date, count: count - 3)
            .as_constraint("Capacity thresholds")) 