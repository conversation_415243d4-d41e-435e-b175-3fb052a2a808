#!/usr/bin/env python3
"""
API tests for appointment scheduler REST endpoints.
"""

import sys
import json
import time
from pathlib import Path
from typing import Dict, Any
import asyncio
import pytest
from fastapi.testclient import TestClient

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from caxl_scheduling_engine.api.app import create_app


class TestAppointmentSchedulerAPI:
    """Test cases for the appointment scheduler API."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        app = create_app()
        return TestClient(app)
    
    def test_root_endpoint(self, client):
        """Test root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert data["message"] == "Appointment Scheduler API"
    
    def test_health_check(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_get_patients(self, client):
        """Test get patients endpoint."""
        response = client.get("/api/v1/patients")
        
        # Should return 200 even if no data files exist (empty list)
        assert response.status_code in [200, 500]  # 500 if data files missing
        
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, list)
            
            # If patients exist, check structure
            if data:
                patient = data[0]
                assert "id" in patient
                assert "name" in patient
                assert "location" in patient
                assert "latitude" in patient["location"]
                assert "longitude" in patient["location"]
    
    def test_get_carestaff(self, client):
        """Test get care staff endpoint."""
        response = client.get("/api/v1/carestaff")
        
        # Should return 200 even if no data files exist (empty list)
        assert response.status_code in [200, 500]  # 500 if data files missing
        
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, list)
            
            # If staff exist, check structure
            if data:
                staff = data[0]
                assert "id" in staff
                assert "name" in staff
                assert "role" in staff
                assert "skills" in staff
                assert "home_location" in staff
                assert isinstance(staff["skills"], list)
    
    def test_assign_appointments_endpoint(self, client):
        """Test assign appointments endpoint."""
        request_data = {
            "appointments": [
                {
                    "consumer_id": "test-consumer-001",
                    "required_skills": ["basic_care"],
                    "duration_min": 30,
                    "priority": "normal"
                }
            ],
            "target_date": "2024-01-15",
            "service_type": "skilled_nursing"
        }
        
        response = client.post("/api/v1/assign-appointments", json=request_data)
        
        # May fail if no data files exist, but should have proper error handling
        assert response.status_code in [200, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "success" in data
            assert "message" in data
            assert "total_appointments" in data
            assert "assigned_appointments" in data
            assert "processing_time_seconds" in data
    
    def test_reassign_appointments_endpoint(self, client):
        """Test reassign appointments endpoint."""
        request_data = {
            "appointment_ids": ["test-appointment-001"],
            "reason": "provider_unavailable",
            "force_unpin": True
        }
        
        response = client.post("/api/v1/reassign", json=request_data)
        
        # May fail if no data files exist, but should have proper error handling
        assert response.status_code in [200, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "success" in data
            assert "message" in data
    
    def test_run_dayplan_endpoint(self, client):
        """Test run dayplan endpoint."""
        request_data = {
            "target_date": "2024-01-15",
            "provider_ids": ["test-provider-001"]
        }
        
        response = client.post("/api/v1/run-dayplan", json=request_data)
        
        # May fail if no data files exist, but should have proper error handling
        assert response.status_code in [200, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "success" in data
            assert "message" in data
            assert "total_appointments" in data
            assert "assigned_appointments" in data
    
    def test_invalid_endpoint(self, client):
        """Test invalid endpoint returns 404."""
        response = client.get("/api/v1/nonexistent")
        assert response.status_code == 404
    
    def test_invalid_method(self, client):
        """Test invalid method returns 405."""
        response = client.delete("/api/v1/patients")
        assert response.status_code == 405


class APIIntegrationTester:
    """Integration tester for API with real scenarios."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = None
    
    def setup_client(self):
        """Setup test client."""
        app = create_app()
        self.client = TestClient(app)
    
    def test_full_workflow(self) -> Dict[str, Any]:
        """Test complete workflow from data loading to assignment."""
        if not self.client:
            self.setup_client()
        
        results = {
            "workflow_test": True,
            "steps": [],
            "success": True,
            "errors": []
        }
        
        try:
            # Step 1: Load patients
            print("📋 Step 1: Loading patients...")
            response = self.client.get("/api/v1/patients")
            if response.status_code == 200:
                patients = response.json()
                results["steps"].append({
                    "step": "load_patients",
                    "success": True,
                    "count": len(patients)
                })
                print(f"  ✅ Loaded {len(patients)} patients")
            else:
                results["steps"].append({
                    "step": "load_patients",
                    "success": False,
                    "error": response.text
                })
                print(f"  ❌ Failed to load patients: {response.status_code}")
            
            # Step 2: Load care staff
            print("👥 Step 2: Loading care staff...")
            response = self.client.get("/api/v1/carestaff")
            if response.status_code == 200:
                carestaff = response.json()
                results["steps"].append({
                    "step": "load_carestaff",
                    "success": True,
                    "count": len(carestaff)
                })
                print(f"  ✅ Loaded {len(carestaff)} care staff")
            else:
                results["steps"].append({
                    "step": "load_carestaff",
                    "success": False,
                    "error": response.text
                })
                print(f"  ❌ Failed to load care staff: {response.status_code}")
            
            # Step 3: Run assignment
            print("🔄 Step 3: Running assignment...")
            assignment_request = {
                "appointments": [
                    {
                        "consumer_id": "test-consumer-001",
                        "required_skills": ["basic_care"],
                        "duration_min": 30,
                        "priority": "normal"
                    }
                ]
            }
            
            response = self.client.post("/api/v1/assign-appointments", json=assignment_request)
            if response.status_code == 200:
                assignment_result = response.json()
                results["steps"].append({
                    "step": "assignment",
                    "success": True,
                    "result": assignment_result
                })
                print(f"  ✅ Assignment completed: {assignment_result.get('assigned_appointments', 0)} assigned")
            else:
                results["steps"].append({
                    "step": "assignment",
                    "success": False,
                    "error": response.text
                })
                print(f"  ❌ Assignment failed: {response.status_code}")
            
            # Step 4: Run day plan
            print("⏰ Step 4: Running day plan...")
            dayplan_request = {
                "target_date": "2024-01-15"
            }
            
            response = self.client.post("/api/v1/run-dayplan", json=dayplan_request)
            if response.status_code == 200:
                dayplan_result = response.json()
                results["steps"].append({
                    "step": "dayplan",
                    "success": True,
                    "result": dayplan_result
                })
                print(f"  ✅ Day plan completed: {dayplan_result.get('assigned_appointments', 0)} time slots assigned")
            else:
                results["steps"].append({
                    "step": "dayplan",
                    "success": False,
                    "error": response.text
                })
                print(f"  ❌ Day plan failed: {response.status_code}")
            
        except Exception as e:
            results["success"] = False
            results["errors"].append(str(e))
            print(f"❌ Workflow test failed: {e}")
        
        # Overall success
        results["success"] = all(step.get("success", False) for step in results["steps"])
        
        return results
    
    def run_performance_test(self, num_requests: int = 10) -> Dict[str, Any]:
        """Run performance test on API endpoints."""
        if not self.client:
            self.setup_client()
        
        print(f"🚀 Running performance test with {num_requests} requests...")
        
        results = {
            "performance_test": True,
            "num_requests": num_requests,
            "endpoints": {},
            "total_time": 0
        }
        
        endpoints = [
            ("/", "GET"),
            ("/health", "GET"),
            ("/api/v1/patients", "GET"),
            ("/api/v1/carestaff", "GET")
        ]
        
        start_time = time.time()
        
        for endpoint, method in endpoints:
            endpoint_results = {
                "method": method,
                "requests": num_requests,
                "times": [],
                "success_count": 0,
                "error_count": 0
            }
            
            for i in range(num_requests):
                request_start = time.time()
                
                try:
                    if method == "GET":
                        response = self.client.get(endpoint)
                    else:
                        response = self.client.post(endpoint, json={})
                    
                    request_time = time.time() - request_start
                    endpoint_results["times"].append(request_time)
                    
                    if response.status_code < 400:
                        endpoint_results["success_count"] += 1
                    else:
                        endpoint_results["error_count"] += 1
                        
                except Exception as e:
                    endpoint_results["error_count"] += 1
                    endpoint_results["times"].append(time.time() - request_start)
            
            # Calculate statistics
            if endpoint_results["times"]:
                endpoint_results["avg_time"] = sum(endpoint_results["times"]) / len(endpoint_results["times"])
                endpoint_results["min_time"] = min(endpoint_results["times"])
                endpoint_results["max_time"] = max(endpoint_results["times"])
            
            results["endpoints"][endpoint] = endpoint_results
            print(f"  {endpoint}: {endpoint_results['success_count']}/{num_requests} successful, avg {endpoint_results.get('avg_time', 0):.3f}s")
        
        results["total_time"] = time.time() - start_time
        print(f"✅ Performance test completed in {results['total_time']:.2f}s")
        
        return results


def main():
    """Main entry point for API tests."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test appointment scheduler API')
    parser.add_argument('--test-type', choices=['unit', 'integration', 'performance', 'all'], 
                       default='all', help='Type of test to run')
    parser.add_argument('--num-requests', type=int, default=10, 
                       help='Number of requests for performance test')
    parser.add_argument('--output', help='Output file for test results (JSON)')
    
    args = parser.parse_args()
    
    results = {}
    
    try:
        if args.test_type in ['integration', 'all']:
            print("🧪 Running API Integration Tests...")
            tester = APIIntegrationTester()
            results["integration"] = tester.test_full_workflow()
        
        if args.test_type in ['performance', 'all']:
            print("⚡ Running API Performance Tests...")
            tester = APIIntegrationTester()
            results["performance"] = tester.run_performance_test(args.num_requests)
        
        if args.test_type in ['unit', 'all']:
            print("🔬 Running API Unit Tests...")
            # Unit tests would be run with pytest
            results["unit"] = {"message": "Run with: pytest tests/test_api.py"}
        
        # Print summary
        print(f"\n📊 API TEST SUMMARY")
        print(f"{'='*40}")
        
        for test_type, result in results.items():
            if isinstance(result, dict) and "success" in result:
                status = "✅ PASSED" if result["success"] else "❌ FAILED"
                print(f"{test_type.upper()}: {status}")
            else:
                print(f"{test_type.upper()}: {result}")
        
        # Save results
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            print(f"📄 Results saved to: {args.output}")
    
    except Exception as e:
        print(f"❌ API tests failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
