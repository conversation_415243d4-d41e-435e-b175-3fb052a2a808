"""
Time Slot Availability Validation Constraint (C010)

This constraint ensures that time slots are available for assignment.
This is a HARD constraint that must be satisfied for valid assignments.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint

from caxl_scheduling_engine.model.planning_models import TimeSlotAssignment


def time_slot_availability(constraint_factory: ConstraintFactory) -> Constraint:
    """Time slot must be available for assignment."""
    return (constraint_factory
            .for_each(TimeSlotAssignment)
            .filter(lambda assignment: assignment.time_slot is not None)
            .filter(lambda assignment: _is_valid_time_slot(assignment.time_slot))
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
            .as_constraint("Time slot availability"))


def _is_valid_time_slot(time_slot) -> bool:
    """Check if time slot is within valid business hours."""
    if time_slot is None:
        return False
    
    # Standard healthcare business hours: 6 AM to 10 PM
    hour = time_slot.hour
    return 6 <= hour < 22