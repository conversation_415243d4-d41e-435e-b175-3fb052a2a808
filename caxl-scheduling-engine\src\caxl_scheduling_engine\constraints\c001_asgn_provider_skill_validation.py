"""
Provider Skill Validation Constraint (C001)

This constraint ensures that providers have all required skills for the appointment type.
This is a HARD constraint that must be satisfied for valid assignments.

Enhanced logic includes:
- Perfect match, Skill hierarchy, No match, Multiple skills, Skill levels
- Empty skills list, Null provider, Invalid skill format, Case sensitivity
"""

from typing import List, Optional

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint

from caxl_scheduling_engine.model.domain import Provider
from caxl_scheduling_engine.model.planning_models import AppointmentAssignment


def required_skills(constraint_factory: ConstraintFactory) -> Constraint:
    """Provider must have all required skills for the appointment type."""
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: assignment.provider is not None and 
                   not _has_required_skills_enhanced(assignment.provider, assignment.appointment_data.required_skills))
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: _calculate_skill_penalty(assignment.provider, assignment.appointment_data.required_skills) if assignment.provider is not None else 1)
            .as_constraint("Required skills"))


def _has_required_skills_enhanced(provider: Provider, required_skills: Optional[List[str]]) -> bool:
    """
    Enhanced skill validation with hierarchy and edge case handling.
    
    Returns:
        bool: True if provider has required skills, False otherwise
    """
    # Handle edge cases
    if provider is None:
        return False
    
    if required_skills is None or len(required_skills) == 0:
        return True  # No skills required means any provider can handle it
    
    if provider.skills is None or len(provider.skills) == 0:
        return False  # Provider has no skills
    
    # Normalize skills for case-insensitive comparison
    provider_skills_normalized = {skill.lower().strip() for skill in provider.skills if skill is not None}
    required_skills_normalized = {skill.lower().strip() for skill in required_skills if skill is not None}
    
    # Check for exact matches first
    if required_skills_normalized.issubset(provider_skills_normalized):
        return True
    
    # Check skill hierarchy (e.g., "nurse" can handle "basic_care")
    skill_hierarchy = {
        "nurse": ["basic_care", "vital_signs", "medication_administration"],
        "rn": ["basic_care", "vital_signs", "medication_administration", "iv_therapy"],
        "lpn": ["basic_care", "vital_signs", "medication_administration"],
        "pt": ["physical_therapy", "mobility_training", "strength_training"],
        "physical_therapist": ["physical_therapy", "mobility_training", "strength_training"],
        "cna": ["personal_care", "mobility_assistance", "housekeeping"],
        "doctor": ["assessment", "medication_management", "wound_care", "diabetes_management"]
    }
    
    # Check if provider has higher-level skills that cover required skills
    for provider_skill in provider_skills_normalized:
        if provider_skill in skill_hierarchy:
            covered_skills = skill_hierarchy[provider_skill]
            covered_skills_normalized = {skill.lower().strip() for skill in covered_skills}
            if required_skills_normalized.issubset(covered_skills_normalized):
                return True
    
    return False


def _calculate_skill_penalty(provider: Provider, required_skills: Optional[List[str]]) -> int:
    """
    Calculate penalty based on skill mismatch severity.
    
    Returns:
        int: Penalty score (higher = more severe mismatch)
    """
    if provider is None or required_skills is None or len(required_skills) == 0:
        return 1  # Base penalty
    
    if provider.skills is None or len(provider.skills) == 0:
        return len(required_skills) * 2  # Severe penalty for provider with no skills
    
    # Normalize skills
    provider_skills_normalized = {skill.lower().strip() for skill in provider.skills if skill is not None}
    required_skills_normalized = {skill.lower().strip() for skill in required_skills if skill is not None}
    
    # Calculate missing skills
    missing_skills = required_skills_normalized - provider_skills_normalized
    
    # Calculate partial matches (skills that are close but not exact)
    partial_matches = 0
    skill_similarity = {
        "medication_management": ["medication_administration"],
        "physical_therapy": ["mobility_training", "strength_training"],
        "personal_care": ["mobility_assistance", "housekeeping"],
        "wound_care": ["basic_care", "assessment"]
    }
    
    for missing_skill in missing_skills:
        if missing_skill in skill_similarity:
            similar_skills = skill_similarity[missing_skill]
            if any(skill in provider_skills_normalized for skill in similar_skills):
                partial_matches += 1
    
    # Penalty calculation: missing skills + partial penalty
    base_penalty = len(missing_skills)
    partial_penalty = partial_matches * 0.5  # Partial matches get reduced penalty
    
    return int(base_penalty + partial_penalty) 