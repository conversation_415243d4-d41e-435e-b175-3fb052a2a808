"""
Timed Appointment Pinning Constraint (C015)

This constraint ensures providers have adequate break time between appointments.
This is a SOFT constraint for optimization preferences.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint, Joiners

from caxl_scheduling_engine.model.planning_models import TimeSlotAssignment


def provider_break_time(constraint_factory: ConstraintFactory) -> Constraint:
    """Ensure providers have adequate break time between appointments."""
    return (constraint_factory
            .for_each(TimeSlotAssignment)
            .join(TimeSlotAssignment,
                  Joiners.equal(lambda assignment: assignment.scheduled_appointment.provider))
            .filter(lambda assignment1, assignment2: (assignment1.id != assignment2.id and
                                                     assignment1.time_slot is not None and
                                                     assignment2.time_slot is not None and
                                                     _insufficient_break_time(assignment1, assignment2)))
            .penalize(HardSoftScore.ONE_SOFT, lambda assignment1, assignment2: 1)
            .as_constraint("Provider break time"))


def _insufficient_break_time(assignment1: TimeSlotAssignment, assignment2: TimeSlotAssignment) -> bool:
    """Check if there's insufficient break time between appointments."""
    if assignment1.time_slot is None or assignment2.time_slot is None:
        return False

    # Calculate time difference between appointments
    # time_slot is a time object, assume 1-hour appointments
    time1 = assignment1.time_slot
    time2 = assignment2.time_slot

    # Convert to minutes for comparison
    minutes1 = time1.hour * 60 + time1.minute
    minutes2 = time2.hour * 60 + time2.minute

    # Assume each appointment is 1 hour, so end time is start time + 60 minutes
    end_minutes1 = minutes1 + 60
    time_diff = minutes2 - end_minutes1

    # Minimum break time: 15 minutes
    min_break_time = 15
    return time_diff < min_break_time