"""
Assignment stage constraints for healthcare scheduling optimization.

This module contains active constraints for the first stage of optimization:
- Assigning providers and dates to appointments

This file now serves as a coordinator that imports and combines constraints
"""

from timefold.solver.score import constraint_provider, ConstraintFactory

# Import individual constraint modules
from .c001_asgn_provider_skill_validation import required_skills
from .c002_asgn_date_based_availability import provider_availability
from .c003_asgn_geographic_service_area import geographic_service_area
from .c004_asgn_timed_visit_date_assignment import provider_role_match
from .c005_asgn_workload_balance_optimization import workload_balancing
from .c006_asgn_geographic_clustering_optimization import capacity_thresholds
from .c007_asgn_patient_preference_matching import patient_preference_matching
from .c008_asgn_provider_capacity_management import provider_capacity_management
from .c009_asgn_continuity_of_care_optimization import continuity_of_care
from caxl_scheduling_engine.utils.config_manager import ConfigManager


@constraint_provider
def define_constraints(constraint_factory: ConstraintFactory):
    """Define all active constraints for the assignment stage."""
    # Get configuration to check feature toggles
    config_manager = ConfigManager()
    scheduler_config = config_manager.get_scheduler_config()
    
    constraints = []
    
    # Hard constraints - must be satisfied (always enabled)
    constraints.append(required_skills(constraint_factory))
    constraints.append(provider_availability(constraint_factory))
    constraints.append(provider_role_match(constraint_factory))
    
    # Soft constraints - optimization preferences (conditional based on feature toggles)
    if scheduler_config.enable_workload_balancing:
        constraints.append(workload_balancing(constraint_factory))
    
    if scheduler_config.enable_geographic_clustering:
        constraints.append(capacity_thresholds(constraint_factory))
        constraints.append(geographic_service_area(constraint_factory))
    
    if scheduler_config.enable_continuity_of_care:
        constraints.append(continuity_of_care(constraint_factory))
    
    # Advanced features (Premium Plan)
    if scheduler_config.enable_patient_preferences:
        constraints.append(patient_preference_matching(constraint_factory))
    
    if scheduler_config.enable_provider_capacity_management:
        constraints.append(provider_capacity_management(constraint_factory))
    
    return constraints 