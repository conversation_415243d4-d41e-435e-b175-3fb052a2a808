#!/usr/bin/env python3
"""
Create Excel documentation for CAXL Scheduling Engine
Similar to the original document with 4 tabs:
1. Domain Model (hierarchical)
2. Constraints (c001-c016)
3. Global Configuration
4. Service Configuration & Mapping
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
import yaml
import inspect
import ast

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "caxl-scheduling-engine" / "src"))

try:
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from openpyxl.utils import get_column_letter
except ImportError:
    print("openpyxl not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "openpyxl"])
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from openpyxl.utils import get_column_letter

# Import domain models
from caxl_scheduling_engine.model.domain import (
    Location, Geofence, DateSpecificProviderAvailability, ShiftPattern,
    ProviderAvailability, ProviderCapacity, ProviderPreferences,
    ConsumerPreferences, Provider, Consumer, AppointmentStatus,
    AppointmentPinning, AppointmentTiming, AppointmentRelationships,
    AppointmentData, AppointmentAssignment, ScheduledAppointment,
    TimeSlotAssignment, AppointmentSchedule, DaySchedule,
    ServiceConfig, SchedulerConfig, GeographicCluster,
    AssignmentResult, BatchAssignmentResult
)

def get_field_info(cls) -> List[Dict[str, Any]]:
    """Extract field information from a class."""
    fields = []
    
    # Get class docstring
    class_doc = cls.__doc__ or ""
    
    # Handle different types of classes
    if hasattr(cls, '__dataclass_fields__'):
        # Dataclass
        for field_name, field_info in cls.__dataclass_fields__.items():
            field_type = str(field_info.type)
            required = field_info.default == inspect._empty
            description = f"Field from {cls.__name__}"
            
            # Try to get field description from comments
            if hasattr(cls, '__annotations__'):
                # Look for comments in the source
                pass
            
            fields.append({
                'field': field_name,
                'type': field_type,
                'required': 'Yes' if required else 'No',
                'description': description,
                'usage': f"Used in {cls.__name__}",
                'example': get_example_value(field_type)
            })
    
    elif hasattr(cls, '__fields__'):
        # Pydantic model
        for field_name, field_info in cls.__fields__.items():
            field_type = str(field_info.type_)
            required = field_info.required
            description = field_info.field_info.description if field_info.field_info else f"Field from {cls.__name__}"
            
            fields.append({
                'field': field_name,
                'type': field_type,
                'required': 'Yes' if required else 'No',
                'description': description,
                'usage': f"Used in {cls.__name__}",
                'example': get_example_value(field_type)
            })
    
    return fields

def get_example_value(field_type: str) -> str:
    """Generate example value based on field type."""
    field_type_lower = field_type.lower()
    
    if 'str' in field_type_lower:
        return '"example_string"'
    elif 'int' in field_type_lower:
        return '42'
    elif 'float' in field_type_lower:
        return '3.14'
    elif 'bool' in field_type_lower:
        return 'True'
    elif 'list' in field_type_lower or '[]' in field_type_lower:
        return '[]'
    elif 'dict' in field_type_lower:
        return '{}'
    elif 'uuid' in field_type_lower:
        return 'uuid.uuid4()'
    elif 'date' in field_type_lower:
        return 'date.today()'
    elif 'time' in field_type_lower:
        return 'time(9, 0)'
    elif 'datetime' in field_type_lower:
        return 'datetime.now()'
    else:
        return 'N/A'

def get_constraint_info() -> List[Dict[str, Any]]:
    """Extract constraint information from constraint files."""
    constraints = []
    
    # Define constraint files and their descriptions
    constraint_files = [
        ('c001_asgn_provider_skill_validation.py', 'Provider Skill Validation'),
        ('c002_asgn_date_based_availability.py', 'Date-based Availability'),
        ('c003_asgn_geographic_service_area.py', 'Geographic Service Area'),
        ('c004_asgn_time_based_availability.py', 'Time-based Availability'),
        ('c004_asgn_timed_visit_date_assignment.py', 'Timed Visit Date Assignment'),
        ('c005_asgn_workload_balance_optimization.py', 'Workload Balance Optimization'),
        ('c006_asgn_geographic_clustering_optimization.py', 'Geographic Clustering Optimization'),
        ('c007_asgn_patient_preference_matching.py', 'Patient Preference Matching'),
        ('c008_asgn_provider_capacity_management.py', 'Provider Capacity Management'),
        ('c009_asgn_continuity_of_care_optimization.py', 'Continuity of Care Optimization'),
        ('c010_schd_timeslot_availability_validation.py', 'Timeslot Availability Validation'),
        ('c011_schd_appointment_overlap_prevention.py', 'Appointment Overlap Prevention'),
        ('c012_schd_flexible_appointment_timing_optimization.py', 'Flexible Appointment Timing Optimization'),
        ('c013_schd_healthcare_task_sequencing.py', 'Healthcare Task Sequencing'),
        ('c014_schd_route_travel_time_optimization.py', 'Route Travel Time Optimization'),
        ('c015_schd_timed_appointment_pinning.py', 'Timed Appointment Pinning'),
        ('c016_schd_route_optimization.py', 'Route Optimization')
    ]
    
    constraints_dir = Path(__file__).parent / "caxl-scheduling-engine" / "src" / "caxl_scheduling_engine" / "constraints"
    
    for filename, description in constraint_files:
        file_path = constraints_dir / filename
        if file_path.exists():
            # Read the file and extract function information
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Parse the file to find functions
            try:
                tree = ast.parse(content)
                functions = []
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        func_name = node.name
                        func_doc = ast.get_docstring(node) or f"Function in {filename}"
                        
                        # Check if it's a constraint implementation function
                        if any(keyword in func_name.lower() for keyword in ['constraint', 'validate', 'check', 'score', 'hard', 'soft']):
                            functions.append({
                                'constraint': description,
                                'function': func_name,
                                'description': func_doc,
                                'example': f"Function: {func_name}()"
                            })
                
                if functions:
                    constraints.extend(functions)
                else:
                    # Add a default entry if no specific functions found
                    constraints.append({
                        'constraint': description,
                        'function': 'main_constraint_logic',
                        'description': f'Main constraint logic for {description}',
                        'example': f'Constraint: {description}'
                    })
                    
            except SyntaxError:
                # If parsing fails, add a basic entry
                constraints.append({
                    'constraint': description,
                    'function': 'constraint_implementation',
                    'description': f'Constraint implementation for {description}',
                    'example': f'Constraint: {description}'
                })
    
    return constraints

def get_global_config_info() -> List[Dict[str, Any]]:
    """Extract global configuration information from scheduler.yml."""
    config_path = Path(__file__).parent / "caxl-scheduling-engine" / "src" / "caxl_scheduling_engine" / "config" / "scheduler.yml"
    
    config_info = []
    
    if config_path.exists():
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)
        
        # Extract configuration parameters
        for key, value in config_data.items():
            if isinstance(value, dict):
                # Handle nested configuration
                for sub_key, sub_value in value.items():
                    config_info.append({
                        'parameter': f"{key}.{sub_key}",
                        'type': type(sub_value).__name__,
                        'default': str(sub_value),
                        'description': f'Configuration for {key}.{sub_key}',
                        'example': str(sub_value)
                    })
            else:
                config_info.append({
                    'parameter': key,
                    'type': type(value).__name__,
                    'default': str(value),
                    'description': f'Global configuration for {key}',
                    'example': str(value)
                })
    
    return config_info

def get_service_config_info() -> List[Dict[str, Any]]:
    """Extract service configuration information."""
    service_configs = []
    
    # Get ServiceConfig fields
    service_config_fields = get_field_info(ServiceConfig)
    
    for field in service_config_fields:
        service_configs.append({
            'parameter': field['field'],
            'level': 'Service',
            'used_by_constraints': get_constraint_usage(field['field']),
            'description': field['description'],
            'example': field['example']
        })
    
    return service_configs

def get_constraint_usage(parameter: str) -> str:
    """Map parameter to constraints that use it."""
    # This is a simplified mapping - in a real implementation, you'd analyze the code
    mapping = {
        'geographic_radius_miles': 'c003_asgn_geographic_service_area, c006_asgn_geographic_clustering_optimization',
        'max_daily_appointments_per_provider': 'c008_asgn_provider_capacity_management',
        'max_weekly_hours_per_provider': 'c008_asgn_provider_capacity_management',
        'continuity_weight': 'c009_asgn_continuity_of_care_optimization',
        'workload_balance_weight': 'c005_asgn_workload_balance_optimization',
        'geographic_clustering_weight': 'c006_asgn_geographic_clustering_optimization',
        'patient_preference_weight': 'c007_asgn_patient_preference_matching',
        'capacity_threshold_percentage': 'c008_asgn_provider_capacity_management'
    }
    
    return mapping.get(parameter, 'Multiple constraints')

def create_excel_document():
    """Create the Excel document with all tabs."""
    wb = Workbook()
    
    # Remove default sheet
    wb.remove(wb.active)
    
    # Create styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Tab 1: Domain Model
    ws1 = wb.create_sheet("Domain Model")
    
    # Headers for Domain Model
    headers1 = ["Field", "Type", "Required", "Description", "Field Usage", "Example"]
    for col, header in enumerate(headers1, 1):
        cell = ws1.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Add domain model data
    row = 2
    
    # Provider and related classes
    provider_classes = [Provider, ProviderAvailability, ProviderPreferences, ProviderCapacity]
    for cls in provider_classes:
        fields = get_field_info(cls)
        for field in fields:
            ws1.cell(row=row, column=1, value=field['field']).border = border
            ws1.cell(row=row, column=2, value=field['type']).border = border
            ws1.cell(row=row, column=3, value=field['required']).border = border
            ws1.cell(row=row, column=4, value=field['description']).border = border
            ws1.cell(row=row, column=5, value=field['usage']).border = border
            ws1.cell(row=row, column=6, value=field['example']).border = border
            row += 1
    
    # Consumer and related classes
    consumer_classes = [Consumer, ConsumerPreferences]
    for cls in consumer_classes:
        fields = get_field_info(cls)
        for field in fields:
            ws1.cell(row=row, column=1, value=field['field']).border = border
            ws1.cell(row=row, column=2, value=field['type']).border = border
            ws1.cell(row=row, column=3, value=field['required']).border = border
            ws1.cell(row=row, column=4, value=field['description']).border = border
            ws1.cell(row=row, column=5, value=field['usage']).border = border
            ws1.cell(row=row, column=6, value=field['example']).border = border
            row += 1
    
    # Appointment and related classes
    appointment_classes = [AppointmentData, AppointmentTiming, AppointmentRelationships, AppointmentPinning]
    for cls in appointment_classes:
        fields = get_field_info(cls)
        for field in fields:
            ws1.cell(row=row, column=1, value=field['field']).border = border
            ws1.cell(row=row, column=2, value=field['type']).border = border
            ws1.cell(row=row, column=3, value=field['required']).border = border
            ws1.cell(row=row, column=4, value=field['description']).border = border
            ws1.cell(row=row, column=5, value=field['usage']).border = border
            ws1.cell(row=row, column=6, value=field['example']).border = border
            row += 1
    
    # Auto-adjust column widths
    for column in ws1.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws1.column_dimensions[column_letter].width = adjusted_width
    
    # Tab 2: Constraints
    ws2 = wb.create_sheet("Constraints")
    
    # Headers for Constraints
    headers2 = ["Constraint", "Function/Test", "Description", "Example"]
    for col, header in enumerate(headers2, 1):
        cell = ws2.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Add constraint data
    constraints = get_constraint_info()
    for row, constraint in enumerate(constraints, 2):
        ws2.cell(row=row, column=1, value=constraint['constraint']).border = border
        ws2.cell(row=row, column=2, value=constraint['function']).border = border
        ws2.cell(row=row, column=3, value=constraint['description']).border = border
        ws2.cell(row=row, column=4, value=constraint['example']).border = border
    
    # Auto-adjust column widths
    for column in ws2.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws2.column_dimensions[column_letter].width = adjusted_width
    
    # Tab 3: Global Configuration
    ws3 = wb.create_sheet("Global Configuration")
    
    # Headers for Global Configuration
    headers3 = ["Parameter", "Type", "Default", "Description", "Example"]
    for col, header in enumerate(headers3, 1):
        cell = ws3.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Add global config data
    global_config = get_global_config_info()
    for row, config in enumerate(global_config, 2):
        ws3.cell(row=row, column=1, value=config['parameter']).border = border
        ws3.cell(row=row, column=2, value=config['type']).border = border
        ws3.cell(row=row, column=3, value=config['default']).border = border
        ws3.cell(row=row, column=4, value=config['description']).border = border
        ws3.cell(row=row, column=5, value=config['example']).border = border
    
    # Auto-adjust column widths
    for column in ws3.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws3.column_dimensions[column_letter].width = adjusted_width
    
    # Tab 4: Service Configuration & Mapping
    ws4 = wb.create_sheet("Service Configuration")
    
    # Headers for Service Configuration
    headers4 = ["Parameter", "Level", "Used By Constraint(s)", "Description", "Example"]
    for col, header in enumerate(headers4, 1):
        cell = ws4.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Add service config data
    service_config = get_service_config_info()
    for row, config in enumerate(service_config, 2):
        ws4.cell(row=row, column=1, value=config['parameter']).border = border
        ws4.cell(row=row, column=2, value=config['level']).border = border
        ws4.cell(row=row, column=3, value=config['used_by_constraints']).border = border
        ws4.cell(row=row, column=4, value=config['description']).border = border
        ws4.cell(row=row, column=5, value=config['example']).border = border
    
    # Auto-adjust column widths
    for column in ws4.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws4.column_dimensions[column_letter].width = adjusted_width
    
    # Save the workbook
    output_path = Path(__file__).parent / "CAXL_Scheduling_Engine_Documentation_v1.xlsx"
    wb.save(output_path)
    print(f"Excel document created successfully: {output_path}")
    
    return output_path

if __name__ == "__main__":
    try:
        output_file = create_excel_document()
        print(f"Documentation Excel file created: {output_file}")
    except Exception as e:
        print(f"Error creating Excel document: {e}")
        import traceback
        traceback.print_exc() 