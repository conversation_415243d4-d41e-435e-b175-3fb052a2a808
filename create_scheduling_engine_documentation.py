#!/usr/bin/env python3
"""
Create Excel documentation for CAXL Scheduling Engine
Similar to the original document with 4 tabs:
1. Domain Model (hierarchical)
2. Constraints (c001-c016)
3. Global Configuration
4. Service Configuration & Mapping
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
import yaml
import inspect
import ast

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "caxl-scheduling-engine" / "src"))

try:
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from openpyxl.utils import get_column_letter
except ImportError:
    print("openpyxl not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "openpyxl"])
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from openpyxl.utils import get_column_letter

# Import domain models
from caxl_scheduling_engine.model.domain import (
    Location, Geofence, DateSpecificProviderAvailability, ShiftPattern,
    ProviderAvailability, ProviderCapacity, ProviderPreferences,
    ConsumerPreferences, Provider, Consumer, AppointmentStatus,
    AppointmentPinning, AppointmentTiming, AppointmentRelationships,
    AppointmentData, AppointmentAssignment, ScheduledAppointment,
    TimeSlotAssignment, AppointmentSchedule, DaySchedule,
    ServiceConfig, SchedulerConfig, GeographicCluster,
    AssignmentResult, BatchAssignmentResult
)

def get_field_info(cls) -> List[Dict[str, Any]]:
    """Extract field information from a class."""
    fields = []
    
    # Get class docstring
    class_doc = cls.__doc__ or ""
    
    # Handle different types of classes
    if hasattr(cls, '__dataclass_fields__'):
        # Dataclass
        for field_name, field_info in cls.__dataclass_fields__.items():
            field_type = str(field_info.type)
            required = field_info.default == inspect._empty
            description = f"Field from {cls.__name__}"
            
            # Try to get field description from comments
            if hasattr(cls, '__annotations__'):
                # Look for comments in the source
                pass
            
            fields.append({
                'field': field_name,
                'type': field_type,
                'required': 'Yes' if required else 'No',
                'description': description,
                'usage': f"Used in {cls.__name__}",
                'example': get_example_value(field_type)
            })
    
    elif hasattr(cls, '__fields__'):
        # Pydantic model
        for field_name, field_info in cls.__fields__.items():
            field_type = str(field_info.type_)
            required = field_info.required
            description = field_info.field_info.description if field_info.field_info else f"Field from {cls.__name__}"
            
            fields.append({
                'field': field_name,
                'type': field_type,
                'required': 'Yes' if required else 'No',
                'description': description,
                'usage': f"Used in {cls.__name__}",
                'example': get_example_value(field_type)
            })
    
    return fields

def get_example_value(field_type: str) -> str:
    """Generate example value based on field type."""
    field_type_lower = field_type.lower()
    
    if 'str' in field_type_lower:
        return '"example_string"'
    elif 'int' in field_type_lower:
        return '42'
    elif 'float' in field_type_lower:
        return '3.14'
    elif 'bool' in field_type_lower:
        return 'True'
    elif 'list' in field_type_lower or '[]' in field_type_lower:
        return '[]'
    elif 'dict' in field_type_lower:
        return '{}'
    elif 'uuid' in field_type_lower:
        return 'uuid.uuid4()'
    elif 'date' in field_type_lower:
        return 'date.today()'
    elif 'time' in field_type_lower:
        return 'time(9, 0)'
    elif 'datetime' in field_type_lower:
        return 'datetime.now()'
    else:
        return 'N/A'

def get_constraint_info() -> List[Dict[str, Any]]:
    """Extract constraint information from constraint files."""
    constraints = []
    
    # Define constraint files and their descriptions
    constraint_files = [
        ('c001_asgn_provider_skill_validation.py', 'Provider Skill Validation'),
        ('c002_asgn_date_based_availability.py', 'Date-based Availability'),
        ('c003_asgn_geographic_service_area.py', 'Geographic Service Area'),
        ('c004_asgn_time_based_availability.py', 'Time-based Availability'),
        ('c004_asgn_timed_visit_date_assignment.py', 'Timed Visit Date Assignment'),
        ('c005_asgn_workload_balance_optimization.py', 'Workload Balance Optimization'),
        ('c006_asgn_geographic_clustering_optimization.py', 'Geographic Clustering Optimization'),
        ('c007_asgn_patient_preference_matching.py', 'Patient Preference Matching'),
        ('c008_asgn_provider_capacity_management.py', 'Provider Capacity Management'),
        ('c009_asgn_continuity_of_care_optimization.py', 'Continuity of Care Optimization'),
        ('c010_schd_timeslot_availability_validation.py', 'Timeslot Availability Validation'),
        ('c011_schd_appointment_overlap_prevention.py', 'Appointment Overlap Prevention'),
        ('c012_schd_flexible_appointment_timing_optimization.py', 'Flexible Appointment Timing Optimization'),
        ('c013_schd_healthcare_task_sequencing.py', 'Healthcare Task Sequencing'),
        ('c014_schd_route_travel_time_optimization.py', 'Route Travel Time Optimization'),
        ('c015_schd_timed_appointment_pinning.py', 'Timed Appointment Pinning'),
        ('c016_schd_route_optimization.py', 'Route Optimization')
    ]
    
    constraints_dir = Path(__file__).parent / "caxl-scheduling-engine" / "src" / "caxl_scheduling_engine" / "constraints"
    
    for filename, description in constraint_files:
        file_path = constraints_dir / filename
        if file_path.exists():
            # Read the file and extract function information
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Parse the file to find functions
            try:
                tree = ast.parse(content)
                functions = []
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        func_name = node.name
                        func_doc = ast.get_docstring(node) or f"Function in {filename}"
                        
                        # Check if it's a constraint implementation function
                        if any(keyword in func_name.lower() for keyword in ['constraint', 'validate', 'check', 'score', 'hard', 'soft']):
                            functions.append({
                                'constraint': description,
                                'function': func_name,
                                'description': func_doc,
                                'example': f"Function: {func_name}()"
                            })
                
                if functions:
                    constraints.extend(functions)
                else:
                    # Add a default entry if no specific functions found
                    constraints.append({
                        'constraint': description,
                        'function': 'main_constraint_logic',
                        'description': f'Main constraint logic for {description}',
                        'example': f'Constraint: {description}'
                    })
                    
            except SyntaxError:
                # If parsing fails, add a basic entry
                constraints.append({
                    'constraint': description,
                    'function': 'constraint_implementation',
                    'description': f'Constraint implementation for {description}',
                    'example': f'Constraint: {description}'
                })
    
    return constraints

def get_global_config_info() -> List[Dict[str, Any]]:
    """Extract global configuration information from scheduler.yml."""
    config_path = Path(__file__).parent / "caxl-scheduling-engine" / "src" / "caxl_scheduling_engine" / "config" / "scheduler.yml"
    
    config_info = []
    
    if config_path.exists():
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)
        
        # Extract configuration parameters
        for key, value in config_data.items():
            if isinstance(value, dict):
                # Handle nested configuration
                for sub_key, sub_value in value.items():
                    config_info.append({
                        'parameter': f"{key}.{sub_key}",
                        'type': type(sub_value).__name__,
                        'default': str(sub_value),
                        'description': f'Configuration for {key}.{sub_key}',
                        'example': str(sub_value)
                    })
            else:
                config_info.append({
                    'parameter': key,
                    'type': type(value).__name__,
                    'default': str(value),
                    'description': f'Global configuration for {key}',
                    'example': str(value)
                })
    
    return config_info

def get_service_config_info() -> List[Dict[str, Any]]:
    """Extract service configuration information."""
    service_configs = []
    
    # Define service config parameters manually since we can't import
    service_params = [
        ('service_type', 'Service', 'c001_asgn_provider_skill_validation', 'Type of healthcare service', '"skilled_nursing"'),
        ('required_skills', 'Service', 'c001_asgn_provider_skill_validation', 'Skills required for this service', '["medication_management", "wound_care"]'),
        ('geographic_radius_miles', 'Service', 'c003_asgn_geographic_service_area, c006_asgn_geographic_clustering_optimization', 'Service area radius in miles', '25.0'),
        ('max_daily_appointments_per_provider', 'Service', 'c008_asgn_provider_capacity_management', 'Maximum appointments per provider per day', '8'),
        ('max_weekly_hours_per_provider', 'Service', 'c008_asgn_provider_capacity_management', 'Maximum hours per provider per week', '40'),
        ('continuity_weight', 'Service', 'c009_asgn_continuity_of_care_optimization', 'Weight for continuity of care optimization', '0.8'),
        ('workload_balance_weight', 'Service', 'c005_asgn_workload_balance_optimization', 'Weight for workload balancing', '0.6'),
        ('geographic_clustering_weight', 'Service', 'c006_asgn_geographic_clustering_optimization', 'Weight for geographic clustering', '0.4'),
        ('patient_preference_weight', 'Service', 'c007_asgn_patient_preference_matching', 'Weight for patient preferences', '0.7'),
        ('capacity_threshold_percentage', 'Service', 'c008_asgn_provider_capacity_management', 'Capacity threshold percentage', '0.9')
    ]
    
    for param, level, constraints, desc, example in service_params:
        service_configs.append({
            'parameter': param,
            'level': level,
            'used_by_constraints': constraints,
            'description': desc,
            'example': example
        })
    
    return service_configs

def create_excel_document():
    """Create the Excel document with all tabs."""
    wb = Workbook()
    
    # Remove default sheet
    wb.remove(wb.active)
    
    # Create styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Tab 1: Domain Model
    ws1 = wb.create_sheet("Domain Model")
    
    # Headers for Domain Model
    headers1 = ["Field", "Type", "Required", "Description", "Field Usage", "Example"]
    for col, header in enumerate(headers1, 1):
        cell = ws1.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Add domain model data manually since we can't import the classes
    domain_data = [
        # Provider fields
        ("id", "UUID", "Yes", "Unique provider identifier", "c001_asgn_provider_skill_validation", "uuid.uuid4()"),
        ("name", "str", "Yes", "Provider name", "c007_asgn_patient_preference_matching", '"Dr. Smith"'),
        ("home_location", "Location", "No", "Provider's home base location", "c003_asgn_geographic_service_area", "Location(lat=40.7128, lng=-74.0060)"),
        ("service_areas", "List[Geofence]", "No", "Service area geofences", "c003_asgn_geographic_service_area", "[]"),
        ("languages", "List[str]", "No", "Languages provider speaks", "c007_asgn_patient_preference_matching", '["English", "Spanish"]'),
        ("transportation", "str", "No", "Transportation method", "c014_schd_route_travel_time_optimization", '"car"'),
        ("availability", "ProviderAvailability", "No", "Provider availability schedule", "c002_asgn_date_based_availability", "ProviderAvailability()"),
        ("current_task_count", "int", "No", "Current number of tasks", "c008_asgn_provider_capacity_management", "0"),
        ("critical", "bool", "No", "Critical provider flag", "c009_asgn_continuity_of_care_optimization", "False"),
        ("current_availability_status", "str", "No", "Real-time availability status", "c002_asgn_date_based_availability", '"AVAILABLE"'),
        ("current_unavailable_until", "datetime", "No", "When provider becomes available", "c002_asgn_date_based_availability", "datetime.now()"),
        ("role", "str", "No", "Healthcare role", "c001_asgn_provider_skill_validation", '"RN"'),
        ("skills", "List[str]", "No", "Provider skills", "c001_asgn_provider_skill_validation", '["medication_management", "wound_care"]'),
        ("capacity", "ProviderCapacity", "No", "Daily workload limits", "c008_asgn_provider_capacity_management", "ProviderCapacity()"),
        ("provider_preferences", "ProviderPreferences", "No", "Provider preferences", "c007_asgn_patient_preference_matching", "ProviderPreferences()"),
        ("properties", "Dict[str, Any]", "No", "Extension point for attributes", "Multiple constraints", "{}"),
        
        # ProviderAvailability fields
        ("primary_shift", "ShiftPattern", "No", "Provider's main shift pattern", "c002_asgn_date_based_availability", "ShiftPattern()"),
        ("additional_shifts", "List[ShiftPattern]", "No", "Extra shifts", "c002_asgn_date_based_availability", "[]"),
        ("working_days", "List[str]", "No", "Days provider works", "c002_asgn_date_based_availability", '["monday", "tuesday"]'),
        ("break_periods", "List[tuple[time, time]]", "No", "Standard break periods", "c013_schd_healthcare_task_sequencing", "[(time(12,0), time(13,0))]"),
        ("split_shifts", "List[tuple[time, time]]", "No", "Split shifts per day", "c002_asgn_date_based_availability", "[]"),
        ("working_hours", "tuple[time, time]", "No", "Simple hour ranges", "c002_asgn_date_based_availability", "(time(8,0), time(17,0))"),
        ("holidays", "List[date]", "No", "Holiday dates", "c002_asgn_date_based_availability", "[]"),
        ("date_specific_availability", "List[DateSpecificProviderAvailability]", "No", "Date-specific overrides", "c002_asgn_date_based_availability", "[]"),
        ("time_off_periods", "List[tuple[date, date]]", "No", "Time off periods", "c002_asgn_date_based_availability", "[]"),
        ("max_hours_per_day", "int", "No", "Max hours per day", "c008_asgn_provider_capacity_management", "8"),
        ("max_hours_per_week", "int", "No", "Max hours per week", "c008_asgn_provider_capacity_management", "40"),
        ("unavailable_time_slots", "List[tuple[time, time]]", "No", "Unavailable time slots", "c010_schd_timeslot_availability_validation", "[]"),
        ("max_appointment_duration_min", "int", "No", "Max appointment duration", "c010_schd_timeslot_availability_validation", "120"),
        ("min_gap_between_assignments", "int", "No", "Travel/prep time", "c011_schd_appointment_overlap_prevention", "30"),
        ("overtime_allowed", "bool", "No", "Overtime policy", "c008_asgn_provider_capacity_management", "False"),
        ("max_overtime_hours_per_week", "int", "No", "Max overtime hours", "c008_asgn_provider_capacity_management", "5"),
        ("on_call_available", "bool", "No", "On-call availability", "c002_asgn_date_based_availability", "False"),
        ("on_call_days", "List[str]", "No", "On-call days", "c002_asgn_date_based_availability", "[]"),
        ("properties", "Dict[str, Any]", "No", "Extension point", "Multiple constraints", "{}"),
        
        # ProviderCapacity fields
        ("max_allocated_task_points_in_day", "int", "No", "Max complexity points per day", "c008_asgn_provider_capacity_management", "27"),
        ("max_tasks_count_in_day", "int", "No", "Max tasks per day", "c008_asgn_provider_capacity_management", "6"),
        ("max_hours_per_day", "int", "No", "Max working hours per day", "c008_asgn_provider_capacity_management", "8"),
        ("max_consecutive_tasks", "int", "No", "Max tasks without break", "c013_schd_healthcare_task_sequencing", "4"),
        ("min_break_between_tasks", "int", "No", "Min minutes between tasks", "c013_schd_healthcare_task_sequencing", "15"),
        ("properties", "Dict[str, Any]", "No", "Extension point", "Multiple constraints", "{}"),
        
        # ProviderPreferences fields
        ("blacklisted_consumers", "List[str]", "No", "Patients provider won't serve", "c007_asgn_patient_preference_matching", "[]"),
        ("preferred_consumers", "List[str]", "No", "Patients provider prefers", "c007_asgn_patient_preference_matching", "[]"),
        ("blackout_areas", "List[str]", "No", "Geographic areas to avoid", "c003_asgn_geographic_service_area", "[]"),
        ("preferred_task_types", "List[str]", "No", "Task types provider prefers", "c001_asgn_provider_skill_validation", "[]"),
        ("blacklisted_task_types", "List[str]", "No", "Task types provider won't do", "c001_asgn_provider_skill_validation", "[]"),
        ("properties", "Dict[str, Any]", "No", "Extension point", "Multiple constraints", "{}"),
        
        # Consumer fields
        ("id", "UUID", "Yes", "Unique consumer identifier", "c007_asgn_patient_preference_matching", "uuid.uuid4()"),
        ("name", "str", "Yes", "Consumer name", "c007_asgn_patient_preference_matching", '"John Doe"'),
        ("location", "Location", "No", "Consumer location", "c003_asgn_geographic_service_area", "Location(lat=40.7128, lng=-74.0060)"),
        ("care_episode_id", "str", "No", "Care episode identifier", "c009_asgn_continuity_of_care_optimization", '"episode_001"'),
        ("consumer_preferences", "ConsumerPreferences", "No", "Consumer preferences", "c007_asgn_patient_preference_matching", "ConsumerPreferences()"),
        ("properties", "Dict[str, Any]", "No", "Extension point", "Multiple constraints", "{}"),
        
        # ConsumerPreferences fields
        ("preferred_days", "List[str]", "No", "Days consumer prefers", "c002_asgn_date_based_availability", '["monday", "wednesday"]'),
        ("preferred_hours", "tuple[time, time]", "No", "Time window consumer prefers", "c012_schd_flexible_appointment_timing_optimization", "(time(9,0), time(17,0))"),
        ("unavailable_days", "List[str]", "No", "Days consumer unavailable", "c002_asgn_date_based_availability", '["saturday", "sunday"]'),
        ("unavailable_hours", "List[tuple[time, time]]", "No", "Unavailable time windows", "c010_schd_timeslot_availability_validation", "[]"),
        ("cultural_considerations", "List[str]", "No", "Cultural needs/preferences", "c007_asgn_patient_preference_matching", "[]"),
        ("language", "str", "No", "Preferred language", "c007_asgn_patient_preference_matching", '"English"'),
        ("gender_preference", "str", "No", "Preferred provider gender", "c007_asgn_patient_preference_matching", '"female"'),
        ("preferred_providers", "List[str]", "No", "Specific providers consumer prefers", "c007_asgn_patient_preference_matching", "[]"),
        ("properties", "Dict[str, Any]", "No", "Extension point", "Multiple constraints", "{}"),
        
        # AppointmentData fields
        ("id", "UUID", "Yes", "Unique appointment identifier", "Multiple constraints", "uuid.uuid4()"),
        ("consumer_id", "UUID", "Yes", "Consumer identifier", "c007_asgn_patient_preference_matching", "uuid.uuid4()"),
        ("appointment_date", "date", "Yes", "Appointment date", "c002_asgn_date_based_availability", "date.today()"),
        ("required_skills", "List[str]", "Yes", "Skills required for appointment", "c001_asgn_provider_skill_validation", '["medication_management"]'),
        ("duration_min", "int", "Yes", "Appointment duration in minutes", "c010_schd_timeslot_availability_validation", "60"),
        ("urgent", "bool", "No", "Urgent appointment flag", "c005_asgn_workload_balance_optimization", "False"),
        ("active", "bool", "No", "Active appointment flag", "Multiple constraints", "True"),
        ("status", "str", "No", "Appointment status", "Multiple constraints", '"PENDING_TO_ASSIGN"'),
        ("location", "Location", "No", "Appointment location", "c003_asgn_geographic_service_area", "Location(lat=40.7128, lng=-74.0060)"),
        ("priority", "str", "No", "Appointment priority", "c005_asgn_workload_balance_optimization", '"normal"'),
        ("task_points", "int", "No", "Complexity points", "c008_asgn_provider_capacity_management", "5"),
        ("required_role", "str", "No", "Required provider role", "c001_asgn_provider_skill_validation", '"RN"'),
        ("timing", "AppointmentTiming", "No", "Timing constraints", "c012_schd_flexible_appointment_timing_optimization", "AppointmentTiming()"),
        ("relationships", "AppointmentRelationships", "No", "Appointment relationships", "c009_asgn_continuity_of_care_optimization", "AppointmentRelationships()"),
        ("pinning", "AppointmentPinning", "No", "Pinning management", "c015_schd_timed_appointment_pinning", "AppointmentPinning()"),
        ("properties", "Dict[str, Any]", "No", "Extension point", "Multiple constraints", "{}"),
        
        # AppointmentTiming fields
        ("is_timed_visit", "bool", "No", "Specific time required", "c015_schd_timed_appointment_pinning", "False"),
        ("preferred_time", "time", "No", "Specific time preference", "c012_schd_flexible_appointment_timing_optimization", "time(9,0)"),
        ("time_flexibility_minutes", "int", "No", "Time variation allowed", "c012_schd_flexible_appointment_timing_optimization", "15"),
        ("earliest_start", "datetime", "No", "Earliest possible start", "c010_schd_timeslot_availability_validation", "datetime.now()"),
        ("latest_end", "datetime", "No", "Latest possible end", "c010_schd_timeslot_availability_validation", "datetime.now()"),
        
        # AppointmentRelationships fields
        ("care_episode_id", "str", "No", "Care episode grouping", "c009_asgn_continuity_of_care_optimization", '"episode_001"'),
        ("related_appointment_ids", "List[str]", "No", "Related appointments", "c009_asgn_continuity_of_care_optimization", "[]"),
        ("prerequisite_appointment_ids", "List[str]", "No", "Dependencies", "c013_schd_healthcare_task_sequencing", "[]"),
        ("sequence_order", "int", "No", "Order within group", "c013_schd_healthcare_task_sequencing", "1"),
        ("same_provider_required", "bool", "No", "Continuity requirement", "c009_asgn_continuity_of_care_optimization", "False"),
        
        # AppointmentPinning fields
        ("is_pinned", "bool", "No", "General pinning flag", "c015_schd_timed_appointment_pinning", "False"),
        ("pin_provider", "bool", "No", "Pin assigned provider", "c015_schd_timed_appointment_pinning", "False"),
        ("pin_date", "bool", "No", "Pin assigned date", "c015_schd_timed_appointment_pinning", "False"),
        ("pin_time", "bool", "No", "Pin assigned time", "c015_schd_timed_appointment_pinning", "False"),
        ("pin_reason", "str", "No", "Reason for pinning", "c015_schd_timed_appointment_pinning", '"in_progress"'),
    ]
    
    row = 2
    for field_data in domain_data:
        ws1.cell(row=row, column=1, value=field_data[0]).border = border
        ws1.cell(row=row, column=2, value=field_data[1]).border = border
        ws1.cell(row=row, column=3, value=field_data[2]).border = border
        ws1.cell(row=row, column=4, value=field_data[3]).border = border
        ws1.cell(row=row, column=5, value=field_data[4]).border = border
        ws1.cell(row=row, column=6, value=field_data[5]).border = border
        row += 1
    
    # Auto-adjust column widths
    for column in ws1.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws1.column_dimensions[column_letter].width = adjusted_width
    
    # Tab 2: Constraints
    ws2 = wb.create_sheet("Constraints")
    
    # Headers for Constraints
    headers2 = ["Constraint", "Function/Test", "Description", "Example"]
    for col, header in enumerate(headers2, 1):
        cell = ws2.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Add constraint data
    constraints = get_constraint_info()
    for row, constraint in enumerate(constraints, 2):
        ws2.cell(row=row, column=1, value=constraint['constraint']).border = border
        ws2.cell(row=row, column=2, value=constraint['function']).border = border
        ws2.cell(row=row, column=3, value=constraint['description']).border = border
        ws2.cell(row=row, column=4, value=constraint['example']).border = border
    
    # Auto-adjust column widths
    for column in ws2.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws2.column_dimensions[column_letter].width = adjusted_width
    
    # Tab 3: Global Configuration
    ws3 = wb.create_sheet("Global Configuration")
    
    # Headers for Global Configuration
    headers3 = ["Parameter", "Type", "Default", "Description", "Example"]
    for col, header in enumerate(headers3, 1):
        cell = ws3.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Add global config data
    global_config = get_global_config_info()
    for row, config in enumerate(global_config, 2):
        ws3.cell(row=row, column=1, value=config['parameter']).border = border
        ws3.cell(row=row, column=2, value=config['type']).border = border
        ws3.cell(row=row, column=3, value=config['default']).border = border
        ws3.cell(row=row, column=4, value=config['description']).border = border
        ws3.cell(row=row, column=5, value=config['example']).border = border
    
    # Auto-adjust column widths
    for column in ws3.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws3.column_dimensions[column_letter].width = adjusted_width
    
    # Tab 4: Service Configuration & Mapping
    ws4 = wb.create_sheet("Service Configuration")
    
    # Headers for Service Configuration
    headers4 = ["Parameter", "Level", "Used By Constraint(s)", "Description", "Example"]
    for col, header in enumerate(headers4, 1):
        cell = ws4.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Add service config data
    service_config = get_service_config_info()
    for row, config in enumerate(service_config, 2):
        ws4.cell(row=row, column=1, value=config['parameter']).border = border
        ws4.cell(row=row, column=2, value=config['level']).border = border
        ws4.cell(row=row, column=3, value=config['used_by_constraints']).border = border
        ws4.cell(row=row, column=4, value=config['description']).border = border
        ws4.cell(row=row, column=5, value=config['example']).border = border
    
    # Auto-adjust column widths
    for column in ws4.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws4.column_dimensions[column_letter].width = adjusted_width
    
    # Save the workbook
    output_path = Path(__file__).parent / "CAXL_Scheduling_Engine_Documentation_v1.xlsx"
    wb.save(output_path)
    print(f"Excel document created successfully: {output_path}")
    
    return output_path

if __name__ == "__main__":
    try:
        output_file = create_excel_document()
        print(f"Documentation Excel file created: {output_file}")
    except Exception as e:
        print(f"Error creating Excel document: {e}")
        import traceback
        traceback.print_exc() 