#!/usr/bin/env python3
"""
Comprehensive test runner for appointment scheduler scenarios.

This script:
1. Iterates through each subfolder under data/scenarios
2. Loads data using data_loader.py
3. Executes the appropriate job (assignment or dayplan)
4. Stores output logs inside the corresponding scenario folder
"""

import sys
import time
import json
import logging
from datetime import date, datetime
from pathlib import Path
from typing import Dict, Any, List
import argparse

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from caxl_scheduling_engine.data.data_loader import DataLoader
from caxl_scheduling_engine.jobs.assign_appointments import AssignAppointmentJob
from caxl_scheduling_engine.jobs.day_plan import DayPlanJob

from caxl_scheduling_engine.utils.config_manager import ConfigManager

import caxl_scheduling_engine.jobs.assign_appointments as assign_module


class ScenarioTestRunner:
    """Test runner for appointment scheduler scenarios."""
    
    def __init__(self, scenarios_dir: str = "data/scenarios", output_logs: bool = True):
        self.scenarios_dir = Path(scenarios_dir)
        self.output_logs = output_logs
        self.results = []
        
        if not self.scenarios_dir.exists():
            raise FileNotFoundError(f"Scenarios directory not found: {self.scenarios_dir}")
    
    def discover_scenarios(self) -> List[Path]:
        """Discover all scenario folders."""
        scenarios = []
        for item in self.scenarios_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                # Check if it has the required YAML files
                required_files = ['providers.yml', 'consumers.yml', 'appointments.yml']
                if all((item / file).exists() for file in required_files):
                    scenarios.append(item)
                else:
                    print(f"⚠️  Skipping {item.name}: missing required YAML files")
        
        return sorted(scenarios)
    
    def setup_logging_for_scenario(self, scenario_name: str) -> str:
        """Setup logging to capture output for a specific scenario."""
        if not self.output_logs:
            return ""
        
        log_file = self.scenarios_dir / scenario_name / f"test_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        # Create file handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        
        # Add to root logger
        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)
        
        return str(log_file)
    
    def cleanup_logging(self):
        """Remove file handlers from logger."""
        root_logger = logging.getLogger()
        handlers_to_remove = [h for h in root_logger.handlers if isinstance(h, logging.FileHandler)]
        for handler in handlers_to_remove:
            root_logger.removeHandler(handler)
            handler.close()
    
    def run_assignment_test(self, scenario_path: Path) -> Dict[str, Any]:
        """Run assignment solver test for a scenario."""
        print(f"  🔄 Running assignment solver...")

        try:
            # Create assignment job with scenario-specific data loading
            config_manager = ConfigManager("config")
            job = AssignAppointmentJob(config_manager=config_manager, daemon_mode=False)

            # Create scenario data loader
            scenario_data_loader = DataLoader(str(scenario_path))

            # Monkey patch the create_demo_data function
            original_create_demo_data = assign_module.create_demo_data
            assign_module.create_demo_data = lambda: scenario_data_loader.load_all_data()

            start_time = time.time()
            result = job.run()
            end_time = time.time()

            # Restore original function
            assign_module.create_demo_data = original_create_demo_data

            return {
                "success": True,
                "job_type": "assignment",
                "processing_time": end_time - start_time,
                "result": result
            }

        except Exception as e:
            return {
                "success": False,
                "job_type": "assignment",
                "error": str(e),
                "processing_time": 0
            }
    
    def run_dayplan_test(self, scenario_path: Path) -> Dict[str, Any]:
        """Run dayplan solver test for a scenario."""
        print(f"  🔄 Running dayplan solver...")

        try:
            # Create dayplan job
            # Note: DayPlan job uses create_demo_data() internally, which loads from default data folder
            # For scenario-specific testing, we would need to modify the job to accept custom data
            job = DayPlanJob(config_folder="config", daemon_mode=False)
            
            start_time = time.time()
            result = job.run(target_date=date.today())
            end_time = time.time()
            
            return {
                "success": True,
                "job_type": "dayplan",
                "processing_time": end_time - start_time,
                "result": result.__dict__ if hasattr(result, '__dict__') else result
            }
            
        except Exception as e:
            return {
                "success": False,
                "job_type": "dayplan",
                "error": str(e),
                "processing_time": 0
            }
    
    def run_scenario_tests(self, scenario_path: Path) -> Dict[str, Any]:
        """Run all tests for a single scenario."""
        scenario_name = scenario_path.name
        print(f"\n📁 Testing scenario: {scenario_name}")
        
        # Setup logging
        log_file = self.setup_logging_for_scenario(scenario_name)
        
        scenario_result = {
            "scenario_name": scenario_name,
            "scenario_path": str(scenario_path),
            "log_file": log_file,
            "timestamp": datetime.now().isoformat(),
            "tests": []
        }
        
        try:
            # Load scenario data for validation
            data_loader = DataLoader(str(scenario_path))
            data = data_loader.load_all_data()
            
            print(f"  📊 Data loaded: {len(data['providers'])} providers, {len(data['consumers'])} consumers, {len(data['appointments'])} appointments")
            
            # Run assignment test
            assignment_result = self.run_assignment_test(scenario_path)
            scenario_result["tests"].append(assignment_result)
            
            if assignment_result["success"]:
                print(f"  ✅ Assignment test passed ({assignment_result['processing_time']:.2f}s)")
            else:
                print(f"  ❌ Assignment test failed: {assignment_result.get('error', 'Unknown error')}")
            
            # Run dayplan test
            dayplan_result = self.run_dayplan_test(scenario_path)
            scenario_result["tests"].append(dayplan_result)
            
            if dayplan_result["success"]:
                print(f"  ✅ Dayplan test passed ({dayplan_result['processing_time']:.2f}s)")
            else:
                print(f"  ❌ Dayplan test failed: {dayplan_result.get('error', 'Unknown error')}")
            
            # Overall scenario success
            scenario_result["success"] = all(test["success"] for test in scenario_result["tests"])
            
        except Exception as e:
            print(f"  ❌ Scenario failed: {e}")
            scenario_result["success"] = False
            scenario_result["error"] = str(e)
        
        finally:
            # Cleanup logging
            self.cleanup_logging()
        
        return scenario_result
    
    def run_all_scenarios(self) -> Dict[str, Any]:
        """Run tests for all discovered scenarios."""
        print("🚀 Starting comprehensive scenario testing...")
        
        scenarios = self.discover_scenarios()
        print(f"📋 Found {len(scenarios)} scenarios to test")
        
        start_time = time.time()
        
        for scenario_path in scenarios:
            scenario_result = self.run_scenario_tests(scenario_path)
            self.results.append(scenario_result)
        
        end_time = time.time()
        
        # Generate summary
        total_scenarios = len(self.results)
        successful_scenarios = sum(1 for r in self.results if r.get("success", False))
        total_tests = sum(len(r.get("tests", [])) for r in self.results)
        successful_tests = sum(sum(1 for t in r.get("tests", []) if t.get("success", False)) for r in self.results)
        
        summary = {
            "total_time": end_time - start_time,
            "total_scenarios": total_scenarios,
            "successful_scenarios": successful_scenarios,
            "failed_scenarios": total_scenarios - successful_scenarios,
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "failed_tests": total_tests - successful_tests,
            "scenarios": self.results
        }
        
        return summary
    
    def print_summary(self, summary: Dict[str, Any]):
        """Print test summary."""
        print(f"\n{'='*60}")
        print("📊 TEST SUMMARY")
        print(f"{'='*60}")
        print(f"⏱️  Total time: {summary['total_time']:.2f} seconds")
        print(f"📁 Scenarios: {summary['successful_scenarios']}/{summary['total_scenarios']} passed")
        print(f"🧪 Tests: {summary['successful_tests']}/{summary['total_tests']} passed")
        
        if summary['failed_scenarios'] > 0:
            print(f"\n❌ Failed scenarios:")
            for result in summary['scenarios']:
                if not result.get('success', False):
                    print(f"  - {result['scenario_name']}: {result.get('error', 'Unknown error')}")
        
        print(f"\n{'='*60}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Run comprehensive scenario tests')
    parser.add_argument('--scenarios-dir', default='data/scenarios', help='Scenarios directory path')
    parser.add_argument('--no-logs', action='store_true', help='Disable log file output')
    parser.add_argument('--scenario', help='Run specific scenario only')
    
    args = parser.parse_args()
    
    try:
        runner = ScenarioTestRunner(args.scenarios_dir, output_logs=not args.no_logs)
        
        if args.scenario:
            # Run specific scenario
            scenario_path = Path(args.scenarios_dir) / args.scenario
            if not scenario_path.exists():
                print(f"❌ Scenario not found: {scenario_path}")
                sys.exit(1)
            
            result = runner.run_scenario_tests(scenario_path)
            print(f"\n📊 Result: {'✅ PASSED' if result['success'] else '❌ FAILED'}")
        else:
            # Run all scenarios
            summary = runner.run_all_scenarios()
            runner.print_summary(summary)
            
            # Save summary to file
            summary_file = Path(args.scenarios_dir) / f"test_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2, default=str)
            print(f"📄 Summary saved to: {summary_file}")
            
            # Exit with error code if any tests failed
            if summary['failed_tests'] > 0:
                sys.exit(1)
    
    except Exception as e:
        print(f"❌ Test runner failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
