# Comprehensive Healthcare Scheduling Scenarios Guide

This guide covers all available test scenarios for the healthcare appointment scheduling system, including comprehensive edge cases for robust testing and demonstration.

## 📋 Scenario Categories

### 🟢 Basic Scenarios
Simple demonstrations for initial demos and basic functionality.

### 🟡 Advanced Scenarios  
Complex scenarios demonstrating specific optimization features.

### 🔴 Edge Case Scenarios
Comprehensive edge cases for testing system robustness and error handling.

## 🟢 Basic Scenarios

### Basic Demo (`basic_demo/`)
**Purpose**: Simple demonstration of core functionality
**Complexity**: Low
**Features**: Basic assignment, skill matching, geographic areas, workload distribution
**Data**: 3 providers, 5 patients, 8 appointments

### Geographic Clustering (`geographic_clustering/`)
**Purpose**: Demonstrate geographic clustering optimization
**Complexity**: Medium  
**Features**: Geographic clustering, service areas, travel optimization
**Data**: 4 providers, 12 patients, 15 appointments (3 clusters)

### Continuity of Care (`continuity_of_care/`)
**Purpose**: Demonstrate continuity of care optimization
**Complexity**: Medium
**Features**: Care episodes, provider-patient relationships, continuity
**Data**: 3 providers, 6 patients, 12 appointments (3 care episodes)

### Patient Preferences (`patient_preferences/`)
**Purpose**: Demonstrate patient preference matching
**Complexity**: Medium
**Features**: Language, gender, cultural, provider preferences
**Data**: 5 providers, 8 patients, 10 appointments

### Capacity Management (`capacity_management/`)
**Purpose**: Demonstrate provider capacity constraints
**Complexity**: Medium
**Features**: Capacity limits, overload prevention, skill-specific capacity
**Data**: 4 providers, 10 patients, 15 appointments

## 🔴 Edge Case Scenarios

### Skill Hierarchy (`skill_hierarchy/`)
**Purpose**: Test skill matching with various edge cases
**Complexity**: High
**Edge Cases**:
- Perfect match, Skill hierarchy, No match, Multiple skills, Skill levels
- Empty skills list, Null provider, Invalid skill format, Case sensitivity
- Duplicate skills, Non-existent skills

**Data**: 8 providers, 12 patients, 20 appointments

### Availability Edge Cases (`availability_edge_cases/`)
**Purpose**: Test availability constraints with edge cases
**Complexity**: High
**Edge Cases**:
- Available date, Unavailable date, Blackout period, Weekend coverage, Holiday
- Invalid date format, Timezone issues, Leap year, DST changes

**Data**: 4 providers, 8 patients, 12 appointments

### Geographic Edge Cases (`geographic_edge_cases/`)
**Purpose**: Test geographic constraints with edge cases
**Complexity**: High
**Edge Cases**:
- Within area, Outside area, Multiple areas, Boundary case, Cross-state
- Invalid coordinates, Null location, International, Rural areas

**Data**: 5 providers, 10 patients, 15 appointments

### Timing Edge Cases (`timing_edge_cases/`)
**Purpose**: Test timing constraints with edge cases
**Complexity**: High
**Edge Cases**:
- Exact time match, Time window, Flexible timing, Strict timing
- Timezone conflicts, DST issues, 24-hour format, Leap seconds

**Data**: 4 providers, 8 patients, 12 appointments

### Workload Edge Cases (`workload_edge_cases/`)
**Purpose**: Test workload balancing with edge cases
**Complexity**: High
**Edge Cases**:
- Within hours, Outside hours, Break time, Overtime, Shift patterns
- Split shifts, On-call hours, Emergency coverage, Holiday schedules

**Data**: 6 providers, 10 patients, 15 appointments

### Overlap Edge Cases (`overlap_edge_cases/`)
**Purpose**: Test appointment overlap handling
**Complexity**: High
**Edge Cases**:
- No overlap, Partial overlap, Complete overlap, Adjacent appointments
- Zero duration, Very long appointments, Concurrent appointments

**Data**: 3 providers, 6 patients, 10 appointments

### Preference Edge Cases (`preference_edge_cases/`)
**Purpose**: Test patient preference handling
**Complexity**: High
**Edge Cases**:
- Perfect match, Partial match, No match, Multiple preferences
- Conflicting preferences, Preference changes, Cultural considerations

**Data**: 5 providers, 8 patients, 12 appointments

### Capacity Edge Cases (`capacity_edge_cases/`)
**Purpose**: Test capacity management edge cases
**Complexity**: High
**Edge Cases**:
- Optimal capacity, Under capacity, Over capacity, Dynamic capacity
- Seasonal capacity, Emergency capacity, Skill-specific capacity

**Data**: 6 providers, 10 patients, 15 appointments

### Continuity Edge Cases (`continuity_edge_cases/`)
**Purpose**: Test continuity of care edge cases
**Complexity**: High
**Edge Cases**:
- Same provider, Different provider, Episode continuity, Care team
- Provider changes, Episode breaks, Multi-provider episodes

**Data**: 4 providers, 8 patients, 12 appointments

### Dependency Edge Cases (`dependency_edge_cases/`)
**Purpose**: Test task dependency handling
**Complexity**: High
**Edge Cases**:
- Sequential order, Parallel tasks, Dependency chain, No dependencies
- Circular dependencies, Long chains, Orphaned tasks, Broken sequences

**Data**: 5 providers, 8 patients, 12 appointments

### Routing Edge Cases (`routing_edge_cases/`)
**Purpose**: Test route optimization edge cases
**Complexity**: High
**Edge Cases**:
- Minimal travel, Efficient route, Multiple stops, Return to base
- Traffic conditions, Road closures, Distance vs time, Fuel efficiency

**Data**: 4 providers, 8 patients, 12 appointments

### Clustering Edge Cases (`clustering_edge_cases/`)
**Purpose**: Test geographic clustering edge cases
**Complexity**: High
**Edge Cases**:
- Tight clusters, Scattered appointments, Regional grouping
- Rural vs urban, Island appointments, Cross-region assignments

**Data**: 5 providers, 10 patients, 15 appointments

## 🛠️ Management Tools

### Scenario Manager (`switch_scenario.py`)
```bash
# List all scenarios
python switch_scenario.py list

# Switch to a scenario
python switch_scenario.py switch <scenario_name>

# Show current scenario
python switch_scenario.py current

# Restore from backup
python switch_scenario.py restore
```

### Demo Script (`demo_scenarios.py`)
```bash
# Run specific demos
python demo_scenarios.py basic
python demo_scenarios.py geographic
python demo_scenarios.py continuity

# Run all demos
python demo_scenarios.py all

# Show feature toggle examples
python demo_scenarios.py toggles
```

### Edge Case Generator (`generate_edge_cases.py`)
```bash
# Generate all edge case scenarios
python generate_edge_cases.py
```

## 💰 Feature Toggle Examples

### Basic Plan
```yaml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: false
enable_provider_capacity_management: false
enable_route_optimization: false
```

### Premium Plan
```yaml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true
enable_route_optimization: false
```

### Enterprise Plan
```yaml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true
enable_route_optimization: true
enable_advanced_traffic_integration: true
```

## 🎯 Demo Strategies

### 1. Progressive Complexity
Start with basic scenarios and progress to edge cases:
```bash
python switch_scenario.py switch basic_demo
python -m src.appointment_scheduler.jobs.assign_appointments

python switch_scenario.py switch geographic_clustering
python -m src.appointment_scheduler.jobs.assign_appointments

python switch_scenario.py switch skill_hierarchy
python -m src.appointment_scheduler.jobs.assign_appointments
```

### 2. Feature Toggle Demonstration
Show how features can be enabled/disabled:
```bash
# Edit config/scheduler.yml to enable/disable features
# Run the same scenario with different configurations
python -m src.appointment_scheduler.jobs.assign_appointments
```

### 3. Edge Case Robustness
Demonstrate system resilience:
```bash
python switch_scenario.py switch availability_edge_cases
python -m src.appointment_scheduler.jobs.assign_appointments

python switch_scenario.py switch geographic_edge_cases
python -m src.appointment_scheduler.jobs.assign_appointments
```

### 4. Comparison Demos
Show before/after with different scenarios:
```bash
# Run basic scenario
python switch_scenario.py switch basic_demo
python -m src.appointment_scheduler.jobs.assign_appointments

# Run advanced scenario
python switch_scenario.py switch continuity_of_care
python -m src.appointment_scheduler.jobs.assign_appointments
```

## 📊 Expected Results by Scenario

### Basic Demo
- All appointments assigned
- Skill matching evident
- Geographic constraints satisfied
- Basic workload balancing

### Geographic Clustering
- Providers assigned to nearest clusters
- Travel times minimized
- Service areas respected
- Clustering evident in schedule

### Continuity of Care
- Same provider across care episodes
- Provider-patient relationships maintained
- Care episodes grouped together
- Continuity prioritized

### Edge Cases
- Graceful error handling
- No system crashes
- Appropriate error messages
- Valid cases still work

## 🔧 Troubleshooting

### Common Issues
1. **Scenario not found**: Check scenario exists in `data/scenarios/`
2. **Jobs fail**: Verify YAML syntax and required files
3. **Feature toggles not working**: Check `config/scheduler.yml`
4. **Edge cases crash system**: Review error handling in constraints

### Debug Steps
1. Check logs in `logs/` directory
2. Verify data files are properly formatted
3. Test with basic scenario first
4. Gradually increase complexity

## 📈 Performance Testing

### Load Testing
Use scenarios with high appointment counts:
- `capacity_management`: 15 appointments
- `geographic_clustering`: 15 appointments
- `skill_hierarchy`: 20 appointments

### Stress Testing
Use edge case scenarios:
- `availability_edge_cases`: Invalid data
- `geographic_edge_cases`: Boundary conditions
- `timing_edge_cases`: Time conflicts

### Regression Testing
Run all scenarios to ensure no regressions:
```bash
python demo_scenarios.py all
```

## 🎓 Training Scenarios

### For New Users
1. Start with `basic_demo`
2. Progress to `geographic_clustering`
3. Show `continuity_of_care`
4. Demonstrate feature toggles

### For Advanced Users
1. Show edge case scenarios
2. Demonstrate error handling
3. Test system limits
4. Explore optimization features

### For Developers
1. Review constraint implementations
2. Test edge case handling
3. Verify error recovery
4. Check performance under load

This comprehensive guide provides everything needed to effectively demonstrate and test the healthcare appointment scheduling system across all scenarios and edge cases. 