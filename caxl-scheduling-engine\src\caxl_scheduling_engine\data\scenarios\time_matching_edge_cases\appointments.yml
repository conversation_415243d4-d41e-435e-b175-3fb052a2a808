appointments:
  # Exact time match requirement - morning
  - consumer_id: "time-consumer-001"
    required_skills: ["medication_management"]
    duration_min: 30
    appointment_date: "2024-01-15"
    priority: "high"
    location:
      latitude: 40.7580
      longitude: -73.9855
      city: "New York"
      state: "NY"
      timezone: "America/New_York"
    timing:
      preferred_time: "09:30"
      flexibility_minutes: 0  # Exact time required
      time_window_start: "09:30"
      time_window_end: "09:30"
      strict_timing: true

  # Windowed time match - afternoon flexibility
  - consumer_id: "time-consumer-002"
    required_skills: ["wound_care"]
    duration_min: 45
    appointment_date: "2024-01-15"
    priority: "normal"
    location:
      latitude: 40.7505
      longitude: -73.9934
      city: "New York"
      state: "NY"
      timezone: "America/New_York"
    timing:
      preferred_time: "15:00"
      flexibility_minutes: 120  # 2-hour flexibility
      time_window_start: "13:00"
      time_window_end: "17:00"
      strict_timing: false

  # Early morning appointment
  - consumer_id: "time-consumer-003"
    required_skills: ["basic_care"]
    duration_min: 60
    appointment_date: "2024-01-16"
    priority: "high"
    location:
      latitude: 40.7829
      longitude: -73.9654
      city: "New York"
      state: "NY"
      timezone: "America/New_York"
    timing:
      preferred_time: "07:00"
      flexibility_minutes: 30
      time_window_start: "06:30"
      time_window_end: "08:00"
      strict_timing: true

  # Cross-timezone appointment (CST)
  - consumer_id: "time-consumer-004"
    required_skills: ["wound_care", "assessment"]
    duration_min: 90
    appointment_date: "2024-01-15"
    priority: "normal"
    location:
      latitude: 41.8781
      longitude: -87.6298
      city: "Chicago"
      state: "IL"
      timezone: "America/Chicago"
    timing:
      preferred_time: "16:00"  # 4 PM CST = 5 PM EST
      flexibility_minutes: 60
      time_window_start: "15:00"
      time_window_end: "18:00"
      strict_timing: false

  # Evening appointment with strict timing (CST)
  - consumer_id: "time-consumer-005"
    required_skills: ["iv_therapy"]
    duration_min: 30
    appointment_date: "2024-01-16"
    priority: "urgent"
    location:
      latitude: 41.8781
      longitude: -87.6298
      city: "Chicago"
      state: "IL"
      timezone: "America/Chicago"
    timing:
      preferred_time: "20:00"  # 8 PM CST
      flexibility_minutes: 0
      time_window_start: "20:00"
      time_window_end: "20:00"
      strict_timing: true

  # Weekend appointment (PST)
  - consumer_id: "time-consumer-006"
    required_skills: ["basic_care", "companionship"]
    duration_min: 120
    appointment_date: "2024-01-20"  # Saturday
    priority: "normal"
    location:
      latitude: 34.0522
      longitude: -118.2437
      city: "Los Angeles"
      state: "CA"
      timezone: "America/Los_Angeles"
    timing:
      preferred_time: "12:00"  # Noon PST
      flexibility_minutes: 180  # 3-hour flexibility
      time_window_start: "10:00"
      time_window_end: "16:00"
      strict_timing: false

  # Conflicting time appointment (should remain unassigned)
  - consumer_id: "time-consumer-001"
    required_skills: ["assessment"]
    duration_min: 45
    appointment_date: "2024-01-15"
    priority: "low"
    location:
      latitude: 40.7580
      longitude: -73.9855
      city: "New York"
      state: "NY"
      timezone: "America/New_York"
    timing:
      preferred_time: "02:00"  # 2 AM - outside working hours
      flexibility_minutes: 0
      time_window_start: "02:00"
      time_window_end: "02:00"
      strict_timing: true

  # Break period conflict appointment
  - consumer_id: "time-consumer-002"
    required_skills: ["medication_management"]
    duration_min: 30
    appointment_date: "2024-01-15"
    priority: "normal"
    location:
      latitude: 40.7505
      longitude: -73.9934
      city: "New York"
      state: "NY"
      timezone: "America/New_York"
    timing:
      preferred_time: "12:30"  # During lunch break
      flexibility_minutes: 15
      time_window_start: "12:15"
      time_window_end: "12:45"
      strict_timing: true
