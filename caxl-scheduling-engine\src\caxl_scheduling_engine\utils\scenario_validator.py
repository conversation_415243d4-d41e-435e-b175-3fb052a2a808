"""
Scenario validation utilities for appointment scheduler.
"""

from pathlib import Path
from typing import Dict, Any, Optional

import yaml

from caxl_scheduling_engine.model.domain import Provider, Consumer, AppointmentData


class ScenarioValidator:
    """Validates scenario data for completeness and correctness."""
    
    REQUIRED_FILES = ['providers.yml', 'consumers.yml', 'appointments.yml']
    OPTIONAL_FILES = ['README.md']
    
    def __init__(self, scenario_path: Path):
        self.scenario_path = Path(scenario_path)
        self.validation_errors = []
        self.validation_warnings = []
    
    def validate_scenario(self) -> Dict[str, Any]:
        """Validate a complete scenario."""
        self.validation_errors.clear()
        self.validation_warnings.clear()
        
        # Check file existence
        self._validate_file_structure()
        
        if not self.validation_errors:
            # Validate file contents
            self._validate_providers_file()
            self._validate_consumers_file()
            self._validate_appointments_file()
            
            # Cross-validate relationships
            self._validate_relationships()
        
        return {
            "valid": len(self.validation_errors) == 0,
            "errors": self.validation_errors,
            "warnings": self.validation_warnings,
            "scenario_path": str(self.scenario_path)
        }
    
    def _validate_file_structure(self):
        """Validate that required files exist."""
        if not self.scenario_path.exists():
            self.validation_errors.append(f"Scenario directory does not exist: {self.scenario_path}")
            return
        
        if not self.scenario_path.is_dir():
            self.validation_errors.append(f"Scenario path is not a directory: {self.scenario_path}")
            return
        
        for required_file in self.REQUIRED_FILES:
            file_path = self.scenario_path / required_file
            if not file_path.exists():
                self.validation_errors.append(f"Required file missing: {required_file}")
            elif not file_path.is_file():
                self.validation_errors.append(f"Required path is not a file: {required_file}")
        
        # Check for README
        readme_path = self.scenario_path / "README.md"
        if not readme_path.exists():
            self.validation_warnings.append("README.md file missing - recommended for documentation")
    
    def _validate_yaml_file(self, filename: str) -> Optional[Dict[str, Any]]:
        """Validate and load a YAML file."""
        file_path = self.scenario_path / filename
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            if not isinstance(data, dict):
                self.validation_errors.append(f"{filename}: Root element must be a dictionary")
                return None
            
            return data
            
        except yaml.YAMLError as e:
            self.validation_errors.append(f"{filename}: Invalid YAML format - {e}")
            return None
        except Exception as e:
            self.validation_errors.append(f"{filename}: Failed to read file - {e}")
            return None
    
    def _validate_providers_file(self):
        """Validate providers.yml file."""
        data = self._validate_yaml_file('providers.yml')
        if not data:
            return
        
        if 'providers' not in data:
            self.validation_errors.append("providers.yml: Missing 'providers' key")
            return
        
        providers = data['providers']
        if not isinstance(providers, list):
            self.validation_errors.append("providers.yml: 'providers' must be a list")
            return
        
        if len(providers) == 0:
            self.validation_warnings.append("providers.yml: No providers defined")
            return
        
        # Validate each provider
        provider_ids = set()
        for i, provider in enumerate(providers):
            self._validate_provider(provider, i, provider_ids)
    
    def _validate_provider(self, provider: Dict[str, Any], index: int, provider_ids: set):
        """Validate a single provider."""
        required_fields = ['id', 'name']
        
        for field in required_fields:
            if field not in provider:
                self.validation_errors.append(f"providers.yml[{index}]: Missing required field '{field}'")
        
        # Check for duplicate IDs
        provider_id = provider.get('id')
        if provider_id:
            if provider_id in provider_ids:
                self.validation_errors.append(f"providers.yml[{index}]: Duplicate provider ID '{provider_id}'")
            else:
                provider_ids.add(provider_id)
        
        # Validate location if present
        if 'home_location' in provider:
            self._validate_location(provider['home_location'], f"providers.yml[{index}].home_location")
        
        # Validate skills
        if 'skills' in provider and not isinstance(provider['skills'], list):
            self.validation_errors.append(f"providers.yml[{index}]: 'skills' must be a list")
    
    def _validate_consumers_file(self):
        """Validate consumers.yml file."""
        data = self._validate_yaml_file('consumers.yml')
        if not data:
            return
        
        if 'consumers' not in data:
            self.validation_errors.append("consumers.yml: Missing 'consumers' key")
            return
        
        consumers = data['consumers']
        if not isinstance(consumers, list):
            self.validation_errors.append("consumers.yml: 'consumers' must be a list")
            return
        
        if len(consumers) == 0:
            self.validation_warnings.append("consumers.yml: No consumers defined")
            return
        
        # Validate each consumer
        consumer_ids = set()
        for i, consumer in enumerate(consumers):
            self._validate_consumer(consumer, i, consumer_ids)
    
    def _validate_consumer(self, consumer: Dict[str, Any], index: int, consumer_ids: set):
        """Validate a single consumer."""
        required_fields = ['id', 'name']
        
        for field in required_fields:
            if field not in consumer:
                self.validation_errors.append(f"consumers.yml[{index}]: Missing required field '{field}'")
        
        # Check for duplicate IDs
        consumer_id = consumer.get('id')
        if consumer_id:
            if consumer_id in consumer_ids:
                self.validation_errors.append(f"consumers.yml[{index}]: Duplicate consumer ID '{consumer_id}'")
            else:
                consumer_ids.add(consumer_id)
        
        # Validate location if present
        if 'location' in consumer:
            self._validate_location(consumer['location'], f"consumers.yml[{index}].location")
    
    def _validate_appointments_file(self):
        """Validate appointments.yml file."""
        data = self._validate_yaml_file('appointments.yml')
        if not data:
            return
        
        if 'appointments' not in data:
            self.validation_errors.append("appointments.yml: Missing 'appointments' key")
            return
        
        appointments = data['appointments']
        if not isinstance(appointments, list):
            self.validation_errors.append("appointments.yml: 'appointments' must be a list")
            return
        
        if len(appointments) == 0:
            self.validation_warnings.append("appointments.yml: No appointments defined")
            return
        
        # Validate each appointment
        for i, appointment in enumerate(appointments):
            self._validate_appointment(appointment, i)
    
    def _validate_appointment(self, appointment: Dict[str, Any], index: int):
        """Validate a single appointment."""
        required_fields = ['consumer_id']
        
        for field in required_fields:
            if field not in appointment:
                self.validation_errors.append(f"appointments.yml[{index}]: Missing required field '{field}'")
        
        # Validate location if present
        if 'location' in appointment:
            self._validate_location(appointment['location'], f"appointments.yml[{index}].location")
        
        # Validate duration
        if 'duration_min' in appointment:
            duration = appointment['duration_min']
            if not isinstance(duration, int) or duration <= 0:
                self.validation_errors.append(f"appointments.yml[{index}]: 'duration_min' must be a positive integer")
    
    def _validate_location(self, location: Dict[str, Any], context: str):
        """Validate a location object."""
        if not isinstance(location, dict):
            self.validation_errors.append(f"{context}: Location must be a dictionary")
            return
        
        required_fields = ['latitude', 'longitude']
        for field in required_fields:
            if field not in location:
                self.validation_errors.append(f"{context}: Missing required field '{field}'")
            else:
                value = location[field]
                if not isinstance(value, (int, float)):
                    self.validation_errors.append(f"{context}: '{field}' must be a number")
    
    def _validate_relationships(self):
        """Validate relationships between providers, consumers, and appointments."""
        # Load all data
        providers_data = self._validate_yaml_file('providers.yml')
        consumers_data = self._validate_yaml_file('consumers.yml')
        appointments_data = self._validate_yaml_file('appointments.yml')
        
        if not all([providers_data, consumers_data, appointments_data]):
            return
        
        # Extract IDs
        provider_ids = {p.get('id') for p in providers_data.get('providers', [])}
        consumer_ids = {c.get('id') for c in consumers_data.get('consumers', [])}
        
        # Validate appointment consumer references
        for i, appointment in enumerate(appointments_data.get('appointments', [])):
            consumer_id = appointment.get('consumer_id')
            if consumer_id and consumer_id not in consumer_ids:
                self.validation_errors.append(
                    f"appointments.yml[{i}]: References unknown consumer_id '{consumer_id}'"
                )
    
    @classmethod
    def validate_all_scenarios(cls, scenarios_dir: Path) -> Dict[str, Any]:
        """Validate all scenarios in a directory."""
        scenarios_dir = Path(scenarios_dir)
        results = {}
        
        if not scenarios_dir.exists():
            return {"error": f"Scenarios directory does not exist: {scenarios_dir}"}
        
        for scenario_path in scenarios_dir.iterdir():
            if scenario_path.is_dir() and not scenario_path.name.startswith('.'):
                validator = cls(scenario_path)
                results[scenario_path.name] = validator.validate_scenario()
        
        # Generate summary
        total_scenarios = len(results)
        valid_scenarios = sum(1 for r in results.values() if r.get('valid', False))
        
        return {
            "summary": {
                "total_scenarios": total_scenarios,
                "valid_scenarios": valid_scenarios,
                "invalid_scenarios": total_scenarios - valid_scenarios
            },
            "scenarios": results
        }
