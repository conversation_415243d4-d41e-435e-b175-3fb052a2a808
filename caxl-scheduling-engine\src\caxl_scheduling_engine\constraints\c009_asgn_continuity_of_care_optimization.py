"""
Continuity of Care Optimization Constraint (C009)

This constraint prefers the same provider for the same patient when possible.
This is a SOFT constraint for optimization preferences.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint, Joiners

from caxl_scheduling_engine.model.planning_models import AppointmentAssignment


def continuity_of_care(constraint_factory: ConstraintFactory) -> Constraint:
    """Prefer same provider for same patient when possible."""
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .join(AppointmentAssignment,
                  Joiners.equal(lambda assignment: assignment.appointment_data.consumer_id))
            .filter(lambda assignment1, assignment2: (assignment1.id != assignment2.id and
                                                     assignment1.provider != assignment2.provider))
            .penalize(HardSoftScore.ONE_SOFT, lambda assignment1, assignment2: 1)
            .as_constraint("Continuity of care")) 