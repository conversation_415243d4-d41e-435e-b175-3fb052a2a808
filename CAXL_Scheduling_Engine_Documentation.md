# CAXL Scheduling Engine - Comprehensive Documentation

## Table of Contents
1. [What is Timefold?](#what-is-timefold)
2. [Timefold Core Concepts](#timefold-core-concepts)
3. [Simple Timefold Example](#simple-timefold-example)
4. [Project Architecture](#project-architecture)
5. [Configuration System](#configuration-system)
6. [Constraint System](#constraint-system)
7. [Two-Job System](#two-job-system)
8. [How It All Works Together](#how-it-all-works-together)

---

## What is Timefold?

Timefold is a constraint satisfaction and optimization solver that helps solve complex scheduling and planning problems. It uses AI-powered algorithms to find optimal solutions while respecting business rules (constraints).

**Key Benefits:**
- Handles complex scheduling with multiple constraints
- Finds optimal solutions automatically
- Scales to handle large datasets
- Provides real-time optimization

---

## Timefold Core Concepts

### Planning Entity
Objects that need to be assigned/scheduled. These have **Planning Variables** that the solver will optimize.

### Planning Variable
Properties of Planning Entities that the solver can change to find optimal solutions.

### Planning Solution
The complete problem definition containing all entities, facts, and the solution score.

### Problem Facts
Static data that doesn't change during solving (providers, time slots, locations).

### Value Range Provider
Defines possible values for Planning Variables.

### Planning Score
Measures solution quality using Hard/Soft constraints:
- **Hard constraints**: Must be satisfied (e.g., provider availability)
- **Soft constraints**: Preferences to optimize (e.g., minimize travel time)

---

## Simple Timefold Example

```python
# Planning Entity - What needs to be scheduled
@planning_entity
@dataclass
class AppointmentAssignment:
    id: Annotated[str, PlanningId]
    appointment_data: AppointmentData
    provider: Annotated[Optional[Provider], PlanningVariable] = None
    assigned_date: Annotated[Optional[date], PlanningVariable] = None

# Planning Solution - Complete problem definition
@planning_solution
@dataclass
class AppointmentSchedule:
    providers: Annotated[List[Provider], ValueRangeProvider]
    available_dates: Annotated[List[date], ValueRangeProvider]
    appointment_assignments: Annotated[List[AppointmentAssignment], PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = None
```

**How it works:**
1. Solver tries different combinations of `provider` and `assigned_date` for each appointment
2. Constraints evaluate each combination and assign scores
3. Solver finds the combination with the best overall score

---

## Project Architecture

```
CAXL Scheduling Engine Architecture

┌─────────────────────────────────────────────────────────────────┐
│                    CAXL Scheduling Engine                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │  Scheduler.py   │    │ Config Manager  │                    │
│  │   (Orchestrator)│◄──►│                 │                    │
│  └─────────────────┘    └─────────────────┘                    │
│           │                       │                             │
│           ▼                       ▼                             │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │ Job 1: Assign   │    │ Job 2: DayPlan  │                    │
│  │ Appointments    │    │                 │                    │
│  │ (2:00 AM)       │    │ (6:00 AM)       │                    │
│  └─────────────────┘    └─────────────────┘                    │
│           │                       │                             │
│           ▼                       ▼                             │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │Assignment       │    │Day Planning     │                    │
│  │Constraints      │    │Constraints      │                    │
│  │(9 constraints)  │    │(7 constraints)  │                    │
│  └─────────────────┘    └─────────────────┘                    │
│           │                       │                             │
│           ▼                       ▼                             │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │Timefold Solver  │    │Timefold Solver  │                    │
│  │(Assignment)     │    │(Time Slots)     │                    │
│  └─────────────────┘    └─────────────────┘                    │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘

Data Flow:
1. Unassigned Appointments → Assignment Job → Appointments with Provider+Date
2. Scheduled Appointments → DayPlan Job → Appointments with Time Slots
```

### Core Components

#### 1. Domain Models (`domain.py`)
Framework-agnostic business entities:
- `AppointmentData`: Core appointment information
- `Provider`: Healthcare providers with skills, availability, capacity
- `Consumer`: Patients/clients requiring services
- `Location`: Geographic locations with coordinates

#### 2. Planning Models (`planning_models.py`)
Timefold-specific wrappers:
- `AppointmentAssignment`: Links appointments to providers and dates
- `TimeSlotAssignment`: Links scheduled appointments to time slots
- `AppointmentSchedule`: Complete assignment solution
- `DaySchedule`: Complete day planning solution

---

## Configuration System

The system uses a two-tier configuration approach:

### Global Configuration (`scheduler.yml`)
```yaml
# System-wide settings
rolling_window_days: 7
max_solving_time_seconds: 300
log_level: "INFO"

# Feature Toggles
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_route_optimization: true
```

### Service-Specific Configuration
Each healthcare service has its own configuration file:

**Example: `behavioral_care.yml`**
```yaml
service_type: "behavioral_care"
required_skills: ["behavioral_health", "crisis_intervention"]
geographic_radius_miles: 25.0
max_daily_appointments_per_provider: 8

# Constraint weights (0.0 to 1.0)
continuity_weight: 0.95
workload_balance_weight: 0.6
geographic_clustering_weight: 0.4

# Service-specific settings
visit_duration_minutes: 45
requires_initial_assessment: true
allows_weekend_visits: true
```

### Configuration Manager (`config_manager.py`)
```python
class ConfigManager:
    def get_scheduler_config() -> SchedulerConfig
    def get_service_config(service_type: str) -> ServiceConfig
    def validate_configuration() -> Dict[str, Any]
    def reload_configuration() -> Dict[str, Any]
```

**Key Features:**
- Automatic configuration loading and validation
- Hot-reload capability for configuration changes
- Feature toggle support for enabling/disabling functionality
- Service-specific customization while maintaining global consistency

---

## Constraint System

Constraints define business rules and optimization preferences. The system has two sets of constraints:

### Assignment Constraints (9 constraints)
Applied during provider/date assignment:

```
Hard Constraints (Must be satisfied):
├── C001: Provider Skill Validation
├── C002: Date-based Availability  
└── C004: Provider Role Match

Soft Constraints (Optimization preferences):
├── C003: Geographic Service Area
├── C005: Workload Balance Optimization
├── C006: Geographic Clustering
├── C007: Patient Preference Matching
├── C008: Provider Capacity Management
└── C009: Continuity of Care
```

### Day Planning Constraints (7 constraints)
Applied during time slot assignment:

```
Hard Constraints:
├── C010: Time Slot Availability
├── C011: Appointment Overlap Prevention
└── C012: Appointment Duration Fit

Soft Constraints:
├── C012: Preferred Hours Optimization
├── C013: Healthcare Task Sequencing
├── C014: Travel Time Optimization
├── C015: Break Time Management
└── C016: Route Optimization
```

### Feature Toggle Integration
```python
@constraint_provider
def define_constraints(constraint_factory: ConstraintFactory):
    config_manager = ConfigManager()
    scheduler_config = config_manager.get_scheduler_config()
    
    constraints = []
    
    # Always enabled hard constraints
    constraints.append(required_skills(constraint_factory))
    constraints.append(provider_availability(constraint_factory))
    
    # Conditionally enabled soft constraints
    if scheduler_config.enable_workload_balancing:
        constraints.append(workload_balancing(constraint_factory))
    
    if scheduler_config.enable_geographic_clustering:
        constraints.append(geographic_clustering(constraint_factory))
    
    return constraints
```

---

## Two-Job System

The system uses a realistic two-stage approach that mirrors real healthcare scheduling workflows:

### Job 1: Assignment Job (`assign_appointments.py`)
**Schedule:** Nightly at 2:00 AM
**Purpose:** Strategic planning for the upcoming week
**Input:** New appointment requests
**Output:** Appointments with provider and date assigned (no specific time)

```
Assignment Job Flow:

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Unassigned      │    │ Timefold        │    │ Assigned        │
│ Appointments    │───►│ Assignment      │───►│ Appointments    │
│                 │    │ Solver          │    │ (Provider+Date) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│• Appointment ID │    │• Skills Match   │    │• Provider: John │
│• Service Type   │    │• Availability   │    │• Date: 2024-01-15│
│• Patient Info   │    │• Geographic     │    │• Time: TBD      │
│• Location       │    │• Workload       │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Key Features:**
- Processes batches of appointments efficiently
- Considers provider skills, availability, and geographic constraints
- Balances workload across providers
- Maintains continuity of care relationships

### Job 2: Day Plan Job (`day_plan.py`)
**Schedule:** Daily at 6:00 AM
**Purpose:** Time slot assignment and route optimization for the day
**Input:** Appointments scheduled for today (with provider and date)
**Output:** Appointments with specific time slots assigned

```
Day Plan Job Flow:

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Scheduled       │    │ Timefold        │    │ Fully Scheduled │
│ Appointments    │───►│ Day Planning    │───►│ Appointments    │
│ (Provider+Date) │    │ Solver          │    │ (Time Slots)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│• Provider: John │    │• Time Slots     │    │• Provider: John │
│• Date: Today    │    │• Travel Time    │    │• Date: Today    │
│• Time: TBD      │    │• Route Optimize │    │• Time: 10:30 AM │
│• Location: A    │    │• No Conflicts   │    │• Duration: 45min│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Key Features:**
- Optimizes daily schedules with specific time slots
- Minimizes travel time between appointments
- Prevents double-booking and scheduling conflicts
- Considers provider break times and preferences

---

## How It All Works Together

### System Workflow

```
Complete Scheduling Workflow:

Day 1 (2:00 AM) - Assignment Job
┌─────────────────────────────────────────────────────────────────┐
│ 1. Load unassigned appointments                                 │
│ 2. Load available providers and their constraints               │
│ 3. Apply assignment constraints (skills, availability, etc.)    │
│ 4. Solve optimization problem                                   │
│ 5. Output: Appointments with Provider + Date (no time)         │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
Day 1 (6:00 AM) - Day Plan Job
┌─────────────────────────────────────────────────────────────────┐
│ 1. Load today's scheduled appointments                          │
│ 2. Generate available time slots for each provider             │
│ 3. Apply day planning constraints (time, travel, conflicts)    │
│ 4. Solve time slot optimization problem                        │
│ 5. Output: Fully scheduled appointments with time slots        │
└─────────────────────────────────────────────────────────────────┘
```

### Configuration Integration

```
Configuration Flow:

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ scheduler.yml   │    │ Config Manager  │    │ Feature Toggles │
│ (Global Config) │───►│                 │───►│ Enable/Disable  │
└─────────────────┘    │                 │    │ Constraints     │
                       │                 │    └─────────────────┘
┌─────────────────┐    │                 │    ┌─────────────────┐
│ service.yml     │───►│                 │───►│ Service-Specific│
│ (Service Config)│    │                 │    │ Parameters      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Scheduler Orchestration (`scheduler.py`)

The main scheduler coordinates both jobs:

```python
class AppointmentScheduler:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.assign_job = AssignAppointmentJob()
        self.day_plan_job = DayPlanJob()

    def setup_schedule(self):
        # Assignment job runs nightly at 2 AM
        schedule.every().day.at("02:00").do(self.run_assign_appointments)

        # Day plan job runs daily at 6 AM
        schedule.every().day.at("06:00").do(self.run_today_day_plan)

    def run_daemon(self):
        while self._running:
            schedule.run_pending()
            time.sleep(60)
```

### Key Benefits of This Architecture

1. **Separation of Concerns**: Assignment and time planning are handled separately
2. **Realistic Workflow**: Mirrors how healthcare scheduling actually works
3. **Scalability**: Each job can be optimized independently
4. **Flexibility**: Feature toggles allow customization per deployment
5. **Maintainability**: Clear separation between configuration, constraints, and logic

### Example End-to-End Flow

```
Monday 2:00 AM - Assignment Job runs:
├── Input: 50 new appointment requests for the week
├── Process: Assign providers and dates using assignment constraints
└── Output: 45 appointments assigned, 5 unassigned (no available providers)

Monday 6:00 AM - Day Plan Job runs:
├── Input: 8 appointments scheduled for Monday
├── Process: Assign time slots using day planning constraints
└── Output: 8 appointments with specific times (9:00 AM, 10:30 AM, etc.)

Tuesday 6:00 AM - Day Plan Job runs:
├── Input: 12 appointments scheduled for Tuesday
├── Process: Optimize time slots and travel routes
└── Output: 12 appointments with optimized schedule
```

This architecture ensures that the CAXL Scheduling Engine can handle complex healthcare scheduling requirements while remaining flexible and maintainable.
