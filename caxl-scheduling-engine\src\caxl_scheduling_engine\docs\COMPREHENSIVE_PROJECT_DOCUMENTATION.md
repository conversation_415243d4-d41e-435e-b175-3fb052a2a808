# CAXL Scheduling Engine - Comprehensive Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Two-Job System](#two-job-system)
4. [Constraints](#constraints)
5. [Scenarios](#scenarios)
6. [Installation & Setup](#installation--setup)
7. [Usage](#usage)
8. [API Documentation](#api-documentation)
9. [Configuration](#configuration)
10. [Testing](#testing)
11. [Development](#development)

---

## Project Overview

The **CAXL Scheduling Engine** is a US-based healthcare appointment scheduling system that uses Timefold optimization with a **two-job architecture** for realistic healthcare scheduling. The system is designed as a backend optimization service that processes healthcare scheduling requests using advanced constraint solving.

### Key Features
- **Two-Job Architecture**: Separates strategic planning from operational scheduling
- **Advanced Constraints**: 16+ healthcare-specific constraints
- **Geographic Optimization**: Service area validation and clustering
- **Continuity of Care**: Maintains provider-patient relationships
- **Patient Preferences**: Respects timing, provider, and location preferences
- **Capacity Management**: Prevents provider overload
- **Route Optimization**: Minimizes travel time and costs
- **Weather Integration**: Adjusts for weather conditions
- **Traffic Awareness**: Accounts for rush hour patterns

### Technology Stack
- **Optimization Engine**: Timefold (Java-based constraint solver)
- **Backend Framework**: FastAPI (Python)
- **Data Models**: Pydantic
- **Configuration**: YAML
- **Logging**: Loguru
- **Testing**: Pytest

---

## Architecture

### Project Structure
```
caxl-scheduling-engine/
├── src/caxl_scheduling_engine/
│   ├── domain.py              # Core data models
│   ├── data_loader.py         # Data loading utilities
│   ├── config_manager.py      # Configuration management
│   ├── scheduler.py           # Main scheduler orchestration
│   ├── planning_models.py     # Timefold optimization models
│   ├── jobs/
│   │   ├── assign_appointments.py  # Assignment job
│   │   └── day_plan.py             # Day planning job
│   ├── constraints/
│   │   ├── base_constraints.py     # Base constraint classes
│   │   ├── assignment_constraints.py  # Assignment constraints
│   │   ├── day_constraints.py      # Day planning constraints
│   │   ├── c001_asgn_provider_skill_validation.py
│   │   ├── c002_asgn_date_based_availability.py
│   │   ├── c003_asgn_geographic_service_area.py
│   │   ├── c004_asgn_timed_visit_date_assignment.py
│   │   ├── c005_asgn_workload_balance_optimization.py
│   │   ├── c006_asgn_geographic_clustering_optimization.py
│   │   ├── c007_asgn_patient_preference_matching.py
│   │   ├── c008_asgn_provider_capacity_management.py
│   │   ├── c009_asgn_continuity_of_care_optimization.py
│   │   ├── c010_schd_timeslot_availability_validation.py
│   │   ├── c011_schd_appointment_overlap_prevention.py
│   │   ├── c012_schd_flexible_appointment_timing_optimization.py
│   │   ├── c013_schd_healthcare_task_sequencing.py
│   │   ├── c014_schd_route_travel_time_optimization.py
│   │   ├── c015_schd_timed_appointment_pinning.py
│   │   └── c016_schd_route_optimization.py
│   ├── api/
│   │   ├── app.py             # FastAPI application
│   │   ├── models.py          # API data models
│   │   └── routes.py          # API endpoints
│   └── utils/
│       ├── scenario_validator.py   # Scenario validation
│       └── test_helpers.py         # Test utilities
├── config/
│   ├── scheduler.yml          # Global configuration
│   ├── skilled_nursing.yml    # Service-specific config
│   └── physical_therapy.yml   # Service-specific config
├── data/
│   ├── appointments.yml       # Appointment data
│   ├── providers.yml          # Provider data
│   ├── consumers.yml          # Consumer data
│   └── scenarios/             # Test scenarios
├── tests/                     # Test suite
├── tools/                     # Analysis tools
└── docs/                      # Documentation
```

### Core Components

#### 1. Domain Models (`domain.py`)
- **Provider**: Healthcare providers with skills, availability, and location
- **Consumer**: Patients with preferences and requirements
- **AppointmentData**: Appointment requests with constraints
- **Location**: Geographic coordinates and service areas
- **ServiceConfig**: Service-specific configurations

#### 2. Data Loader (`data_loader.py`)
- Loads YAML data files
- Validates data integrity
- Supports scenario-based data loading

#### 3. Configuration Manager (`config_manager.py`)
- Manages global and service-specific configurations
- Supports feature toggles
- Handles configuration validation

#### 4. Planning Models (`planning_models.py`)
- Timefold optimization models
- AppointmentAssignment: Links appointments to providers and dates
- TimeSlot: Represents available time slots

---

## Two-Job System

The system uses two separate jobs to handle appointment scheduling, matching real healthcare workflows:

### 1. AssignAppointment Job (Nightly - 2:00 AM)
**Purpose**: Strategic planning for the upcoming week
**Input**: New appointment requests
**Output**: Appointments with date + provider assigned (no specific time)
**Constraints**: Skills, availability, geographic areas, capacity

**Process**:
1. Load new appointment requests
2. Validate provider skills and availability
3. Check geographic service areas
4. Apply workload balancing
5. Optimize for continuity of care
6. Assign date and provider to each appointment

### 2. DayPlan Job (Daily - 6:00 AM)
**Purpose**: Daily operational planning
**Input**: Appointments already assigned to that day with providers
**Output**: Complete schedule with specific times
**Constraints**: Time slots, overlap prevention, sequencing

**Process**:
1. Load appointments for the target date
2. Validate time slot availability
3. Prevent provider double-booking
4. Optimize appointment sequencing
5. Apply route optimization
6. Assign specific time slots

### Benefits
- **Realistic Workflow**: Matches actual healthcare scheduling practices
- **Flexibility**: Handles last-minute changes and urgent appointments
- **Efficiency**: Separates strategic from operational planning
- **Scalability**: Each job optimized independently
- **Reliability**: If one job fails, the other can still run

---

## Constraints

The system implements 16 healthcare-specific constraints across two stages:

### Assignment Stage Constraints (C001-C009)

#### C001: Provider Skill Validation (HARD)
**Purpose**: Ensures providers have required skills for appointments
**Checks**:
- Skill presence validation
- Partial skill matching (substitutions)
- Penalty calculation for missing skills

#### C002: Date-Based Availability (HARD)
**Purpose**: Ensures providers are available on assigned dates
**Checks**:
- Basic availability validation
- Working day verification
- Blackout period checking
- Holiday validation

#### C003: Geographic Service Area (SOFT)
**Purpose**: Ensures providers are within reasonable service area
**Checks**:
- Distance calculation (Haversine)
- Service radius determination (25 miles base)
- Provider type adjustments
- Area type adjustments (rural/urban)

#### C004: Provider Role Match (HARD)
**Purpose**: Ensures provider roles match appointment requirements
**Checks**:
- Role validation
- Required role matching

#### C005: Workload Balance Optimization (SOFT)
**Purpose**: Balances workload across providers
**Checks**:
- Daily task count limits
- Provider capacity validation

#### C006: Geographic Clustering Optimization (SOFT)
**Purpose**: Maintains capacity thresholds per service type
**Checks**:
- Service type grouping
- Capacity threshold validation (max 3 per date per service)

#### C007: Patient Preference Matching (SOFT)
**Purpose**: Matches patient preferences
**Checks**:
- Timing preferences (morning/evening)
- Priority and urgency matching
- Location preferences
- Custom preferences (language, culture)

#### C008: Provider Capacity Management (SOFT)
**Purpose**: Manages provider capacity and workload limits
**Checks**:
- Daily task count limits
- Daily task points limits
- Daily/weekly hours limits
- Emergency capacity reserves
- Specialization capacity

#### C009: Continuity of Care Optimization (SOFT)
**Purpose**: Prefers same provider for same patient
**Checks**:
- Patient provider consistency
- Provider change penalties

### Day Planning Stage Constraints (C010-C016)

#### C010: Time Slot Availability Validation (HARD)
**Purpose**: Ensures time slots are available
**Checks**:
- Slot availability verification

#### C011: Appointment Overlap Prevention (HARD)
**Purpose**: Prevents provider double-booking
**Checks**:
- Double booking detection
- Overlap penalties

#### C012: Flexible Appointment Timing Optimization (SOFT)
**Purpose**: Optimizes appointment timing preferences
**Checks**:
- Preferred time matching
- Flexibility requirements

#### C013: Healthcare Task Sequencing (SOFT)
**Purpose**: Optimizes appointment order for efficiency
**Checks**:
- Task dependencies
- Provider efficiency
- Travel optimization

#### C014: Route Travel Time Optimization (SOFT)
**Purpose**: Minimizes travel time between appointments
**Checks**:
- Travel time calculation
- Route optimization
- Traffic considerations

#### C015: Timed Appointment Pinning (HARD)
**Purpose**: Ensures timed appointments get specific times
**Checks**:
- Timed appointment validation
- Time slot assignment

#### C016: Route Optimization (SOFT)
**Purpose**: Advanced route optimization with multiple factors
**Checks**:
- Multi-stop route optimization
- Traffic pattern integration
- Weather impact consideration
- Fuel efficiency

---

## Scenarios

The system includes 25+ test scenarios demonstrating different features:

### 🟢 Basic Scenarios

#### Basic Demo (`basic_demo/`)
- **Purpose**: Simple demonstration of core functionality
- **Features**: Basic assignment, skill matching, geographic areas
- **Complexity**: Low
- **Best for**: Initial demos, basic feature overview

### 🟡 Core Feature Scenarios

#### Geographic Clustering (`geographic_clustering/`)
- **Purpose**: Demonstrate geographic clustering optimization
- **Features**: Geographic clustering, service areas, travel optimization
- **Complexity**: Medium

#### Continuity of Care (`continuity_of_care/`)
- **Purpose**: Demonstrate continuity of care optimization
- **Features**: Care episodes, provider-patient relationships
- **Complexity**: Medium

#### Patient Preferences (`patient_preferences/`)
- **Purpose**: Demonstrate patient preference matching
- **Features**: Language, gender, cultural, provider preferences
- **Complexity**: Medium

#### Capacity Management (`capacity_management/`)
- **Purpose**: Demonstrate provider capacity constraints
- **Features**: Capacity thresholds, overload prevention
- **Complexity**: Medium

#### Workload Balancing (`workload_balancing/`)
- **Purpose**: Demonstrate workload distribution optimization
- **Features**: Workload balancing, capacity management
- **Complexity**: Medium

### 🔴 Advanced Scenarios

#### Route Optimization (`route_optimization/`)
- **Purpose**: Demonstrate advanced route optimization
- **Features**: Route optimization, travel time minimization
- **Complexity**: High

#### Weather Impact (`weather_impact/`)
- **Purpose**: Demonstrate weather-aware scheduling
- **Features**: Weather integration, travel time adjustments
- **Complexity**: Medium

#### Rush Hour Traffic (`rush_hour_traffic/`)
- **Purpose**: Demonstrate traffic-aware scheduling
- **Features**: Traffic patterns, time-based adjustments
- **Complexity**: Medium

### 🔴 Edge Case Scenarios

#### Availability Edge Cases (`availability_edge_cases/`)
- **Purpose**: Test availability constraint edge cases
- **Features**: Complex availability patterns, blackout periods

#### Geographic Edge Cases (`geographic_edge_cases/`)
- **Purpose**: Test geographic constraint edge cases
- **Features**: Boundary conditions, cross-state assignments

#### Overlap Edge Cases (`overlap_edge_cases/`)
- **Purpose**: Test overlap prevention edge cases
- **Features**: Complex scheduling conflicts

#### Time Matching Edge Cases (`time_matching_edge_cases/`)
- **Purpose**: Test time matching edge cases
- **Features**: Complex timing requirements

#### Provider Assignment Edge Cases (`provider_assignment_edge_cases/`)
- **Purpose**: Test provider assignment edge cases
- **Features**: Complex provider constraints

### 🔴 Specialized Scenarios

#### Skill Hierarchy (`skill_hierarchy/`)
- **Purpose**: Test skill-based assignment logic
- **Features**: Skill hierarchies, substitutions

#### Pinned Appointments (`pinned_appointments/`)
- **Purpose**: Test appointment pinning functionality
- **Features**: Fixed appointments, time constraints

#### Provider Unavailability (`provider_unavailability/`)
- **Purpose**: Test provider unavailability handling
- **Features**: Absence management, backup assignments

---

## Installation & Setup

### Prerequisites
- Python 3.8+
- Java 11+ (for Timefold)
- Git

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd caxl-scheduling-engine

# Install dependencies
pip install -e .

# Install optional dependencies
pip install -e ".[dev,api]"
```

### Configuration

#### Global Configuration (`config/scheduler.yml`)
```yaml
# System-wide settings
rolling_window_days: 7
batch_size: 100
max_solving_time_seconds: 300
log_level: INFO

# Feature toggles
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true
enable_route_optimization: true

# Scheduling times
assign_appointment_time: "02:00"
day_plan_time: "06:00"
```

#### Service-Specific Configuration
- `config/skilled_nursing.yml` - Skilled nursing service settings
- `config/physical_therapy.yml` - Physical therapy service settings

---

## Usage

### Running Jobs

#### 1. AssignAppointment Job
```bash
# Run once
python -m caxl_scheduling_engine.jobs.assign_appointments

# Via scheduler
python -m caxl_scheduling_engine.scheduler --mode once --job assign
```

#### 2. DayPlan Job
```bash
# Run for today
python -m caxl_scheduling_engine.jobs.day_plan

# Run for specific date
python -m caxl_scheduling_engine.jobs.day_plan --date 2024-01-15

# Via scheduler
python -m caxl_scheduling_engine.scheduler --mode once --job dayplan --date 2024-01-15
```

#### 3. Scheduler Daemon
```bash
# Run as daemon (runs both jobs on schedule)
python -m caxl_scheduling_engine.scheduler --mode daemon
```

### Running Scenarios

#### 1. Switch Scenario
```bash
# Switch to basic demo
python switch_scenario.py switch basic_demo

# Switch to geographic clustering
python switch_scenario.py switch geographic_clustering
```

#### 2. Run Demo
```bash
# Run basic demo
python demo_scenarios.py

# Run with feature toggles
python demo_toggles.py
```

### Command Line Scripts
```bash
# Assignment job
assign-appointments

# Day plan job
day-plan

# Scheduler
scheduler

# API server
api-server

# Test scenarios
test-scenarios
```

---

## API Documentation

### REST API Endpoints

#### Health Check
```http
GET /health
```

#### Assignment Job
```http
POST /jobs/assign
Content-Type: application/json

{
  "date": "2024-01-15",
  "force_run": false
}
```

#### Day Plan Job
```http
POST /jobs/dayplan
Content-Type: application/json

{
  "date": "2024-01-15",
  "force_run": false
}
```

#### Get Appointments
```http
GET /appointments?date=2024-01-15&provider_id=123
```

#### Get Providers
```http
GET /providers?skills=skilled_nursing&location=40.7128,-74.0060
```

#### Get Consumers
```http
GET /consumers?location=40.7128,-74.0060
```

### API Models

#### AppointmentAssignment
```python
class AppointmentAssignment(BaseModel):
    appointment_id: str
    provider_id: str
    date: date
    time_slot: Optional[time] = None
    score: Optional[int] = None
    constraints_satisfied: List[str] = []
    constraints_violated: List[str] = []
```

#### Provider
```python
class Provider(BaseModel):
    id: str
    name: str
    skills: List[str]
    location: Location
    availability: Dict[str, List[Dict[str, Any]]]
    capacity: ProviderCapacity
```

#### Consumer
```python
class Consumer(BaseModel):
    id: str
    name: str
    location: Location
    preferences: ConsumerPreferences
    appointments: List[AppointmentData]
```

### Starting API Server
```bash
# Development
python -m caxl_scheduling_engine.api.app

# Production
uvicorn caxl_scheduling_engine.api.app:create_app --host 0.0.0.0 --port 8000
```

---

## Configuration

### Feature Toggles

#### Basic Plan
```yaml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: false
enable_provider_capacity_management: false
enable_route_optimization: false
```

#### Premium Plan
```yaml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true
enable_route_optimization: false
```

#### Enterprise Plan
```yaml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true
enable_route_optimization: true
enable_advanced_traffic_integration: true
```

### Service Types

The system supports multiple healthcare service types:
- **Skilled Nursing**: Home health nursing services
- **Physical Therapy**: Rehabilitation and therapy services
- **Occupational Therapy**: Daily living skills therapy
- **Speech Therapy**: Communication and swallowing therapy
- **Home Health Aide**: Personal care services
- **Medical Social Work**: Social services and counseling

---

## Testing

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=caxl_scheduling_engine --cov-report=html

# Run specific test file
pytest tests/test_basic_functionality.py

# Run API tests
pytest tests/test_api.py
```

### Test Scenarios
```bash
# Run scenario tests
python tests/test_runner.py

# Run special test cases
python tests/special_test_cases.py
```

### Validation
```bash
# Validate scenario structure
python -c "
from caxl_scheduling_engine.utils.scenario_validator import ScenarioValidator
result = ScenarioValidator.validate_all_scenarios('data/scenarios')
print(f'Valid scenarios: {result[\"summary\"][\"valid_scenarios\"]}/{result[\"summary\"][\"total_scenarios\"]}')
"
```

---

## Development

### Project Setup
```bash
# Install development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install

# Run linting
black src/ tests/
isort src/ tests/
flake8 src/ tests/
mypy src/
```

### Adding New Constraints

1. Create constraint file in `src/caxl_scheduling_engine/constraints/`
2. Implement constraint function with Timefold decorators
3. Add constraint to appropriate constraint collection
4. Add tests for the constraint
5. Update documentation

### Adding New Scenarios

1. Create scenario directory in `data/scenarios/`
2. Add `appointments.yml`, `providers.yml`, `consumers.yml`
3. Add `README.md` with scenario description
4. Test scenario with validation tools
5. Update scenario documentation

### Performance Optimization

#### Memory Profiling
```bash
python -m memory_profiler -m caxl_scheduling_engine.jobs.assign_appointments
```

#### Performance Testing
```bash
# Run performance tests
pytest tests/ -m "performance"

# Profile specific functions
python -m cProfile -o profile.stats demo_scenarios.py
```

### Logging

The system uses structured logging with Loguru:
```python
from loguru import logger

logger.info("Processing assignment job", batch_id=batch_id, count=len(appointments))
logger.warning("Provider overload detected", provider_id=provider.id, load=current_load)
logger.error("Constraint violation", constraint="C001", details=violation_details)
```

### Monitoring

#### Key Metrics
- Assignment success rate
- Constraint violation counts
- Processing time per job
- Provider utilization rates
- Geographic distribution metrics

#### Health Checks
- Data integrity validation
- Constraint system health
- Optimization engine status
- API endpoint availability

---

## Troubleshooting

### Common Issues

#### Import Errors
```bash
# Ensure package is installed in development mode
pip install -e .

# Check Python path
python -c "import sys; print(sys.path)"
```

#### Timefold Issues
```bash
# Check Java installation
java -version

# Verify Timefold installation
python -c "import timefold; print(timefold.__version__)"
```

#### Configuration Issues
```bash
# Validate configuration
python -c "
from caxl_scheduling_engine.config_manager import ConfigManager
config = ConfigManager('config')
print('Configuration loaded successfully')
"
```

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Run with verbose output
python -m caxl_scheduling_engine.jobs.assign_appointments --verbose
```

### Support

For issues and questions:
1. Check the logs in `logs/` directory
2. Review constraint documentation
3. Test with basic scenarios
4. Validate data integrity
5. Check configuration settings

---

## License

This project is proprietary software. All rights reserved.

---

*This comprehensive documentation covers the complete CAXL Scheduling Engine system, including architecture, constraints, scenarios, and usage instructions. For specific implementation details, refer to the source code and individual constraint files.* 