"""
Test helper utilities for appointment scheduler.
"""

import shutil
import tempfile
from datetime import date
from pathlib import Path
from typing import Dict, Any, List

import yaml

from caxl_scheduling_engine.data.data_loader import DataLoader
from caxl_scheduling_engine.model.domain import Provider, Consumer, AppointmentData, Location


class TestHelpers:
    """Helper utilities for testing appointment scheduler."""
    
    @staticmethod
    def create_temp_scenario(scenario_data: Dict[str, Any]) -> Path:
        """Create a temporary scenario directory with test data."""
        temp_dir = Path(tempfile.mkdtemp())
        
        # Write providers.yml
        if 'providers' in scenario_data:
            with open(temp_dir / 'providers.yml', 'w') as f:
                yaml.dump({'providers': scenario_data['providers']}, f)
        
        # Write consumers.yml
        if 'consumers' in scenario_data:
            with open(temp_dir / 'consumers.yml', 'w') as f:
                yaml.dump({'consumers': scenario_data['consumers']}, f)
        
        # Write appointments.yml
        if 'appointments' in scenario_data:
            with open(temp_dir / 'appointments.yml', 'w') as f:
                yaml.dump({'appointments': scenario_data['appointments']}, f)
        
        return temp_dir
    
    @staticmethod
    def cleanup_temp_scenario(scenario_path: Path):
        """Clean up temporary scenario directory."""
        if scenario_path.exists():
            shutil.rmtree(scenario_path)
    
    @staticmethod
    def create_minimal_provider_data() -> Dict[str, Any]:
        """Create minimal provider data for testing."""
        return {
            'id': 'test-provider-001',
            'name': 'Test Provider',
            'role': 'RN',
            'skills': ['basic_care'],
            'home_location': {
                'latitude': 40.7589,
                'longitude': -73.9851,
                'city': 'New York',
                'state': 'NY'
            },
            'availability': {
                'working_days': ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                'working_hours': ['08:00', '17:00']
            },
            'capacity': {
                'max_hours_per_day': 8,
                'max_tasks_count_in_day': 6
            }
        }
    
    @staticmethod
    def create_minimal_consumer_data() -> Dict[str, Any]:
        """Create minimal consumer data for testing."""
        return {
            'id': 'test-consumer-001',
            'name': 'Test Consumer',
            'location': {
                'latitude': 40.7580,
                'longitude': -73.9855,
                'city': 'New York',
                'state': 'NY'
            },
            'care_episode_id': 'test-episode-001'
        }
    
    @staticmethod
    def create_minimal_appointment_data() -> Dict[str, Any]:
        """Create minimal appointment data for testing."""
        return {
            'consumer_id': 'test-consumer-001',
            'required_skills': ['basic_care'],
            'duration_min': 30,
            'appointment_date': date.today().strftime('%Y-%m-%d'),
            'location': {
                'latitude': 40.7580,
                'longitude': -73.9855,
                'city': 'New York',
                'state': 'NY'
            }
        }
    
    @staticmethod
    def create_minimal_scenario() -> Dict[str, Any]:
        """Create a complete minimal scenario for testing."""
        return {
            'providers': [TestHelpers.create_minimal_provider_data()],
            'consumers': [TestHelpers.create_minimal_consumer_data()],
            'appointments': [TestHelpers.create_minimal_appointment_data()]
        }
    
    @staticmethod
    def create_edge_case_scenario(case_type: str) -> Dict[str, Any]:
        """Create edge case scenarios for testing."""
        base_scenario = TestHelpers.create_minimal_scenario()
        
        if case_type == "no_matching_skills":
            # Provider has different skills than appointment requires
            base_scenario['providers'][0]['skills'] = ['advanced_care']
            base_scenario['appointments'][0]['required_skills'] = ['basic_care']
        
        elif case_type == "unavailable_provider":
            # Provider not available on appointment date
            base_scenario['providers'][0]['availability']['working_days'] = ['saturday', 'sunday']
        
        elif case_type == "geographic_mismatch":
            # Provider and consumer in different locations
            base_scenario['providers'][0]['home_location'] = {
                'latitude': 34.0522, 'longitude': -118.2437,  # Los Angeles
                'city': 'Los Angeles', 'state': 'CA'
            }
        
        elif case_type == "overloaded_provider":
            # Multiple appointments for single provider
            appointments = []
            for i in range(10):  # Create many appointments
                apt = TestHelpers.create_minimal_appointment_data()
                apt['consumer_id'] = f'test-consumer-{i:03d}'
                appointments.append(apt)
            
            consumers = []
            for i in range(10):
                consumer = TestHelpers.create_minimal_consumer_data()
                consumer['id'] = f'test-consumer-{i:03d}'
                consumer['name'] = f'Test Consumer {i}'
                consumers.append(consumer)
            
            base_scenario['appointments'] = appointments
            base_scenario['consumers'] = consumers
        
        return base_scenario
    
    @staticmethod
    def validate_assignment_result(result: Dict[str, Any]) -> List[str]:
        """Validate assignment job result structure."""
        errors = []
        
        required_keys = ['summary', 'processing_time', 'assignments']
        for key in required_keys:
            if key not in result:
                errors.append(f"Missing required key: {key}")
        
        if 'summary' in result:
            summary = result['summary']
            summary_keys = ['total_appointments', 'assigned_appointments', 'unassigned_appointments']
            for key in summary_keys:
                if key not in summary:
                    errors.append(f"Missing summary key: {key}")
        
        return errors
    
    @staticmethod
    def validate_dayplan_result(result: Any) -> List[str]:
        """Validate dayplan job result structure."""
        errors = []
        
        if not hasattr(result, 'total_appointments'):
            errors.append("Missing total_appointments attribute")
        
        if not hasattr(result, 'assigned_appointments'):
            errors.append("Missing assigned_appointments attribute")
        
        if not hasattr(result, 'processing_time_seconds'):
            errors.append("Missing processing_time_seconds attribute")
        
        return errors
    
    @staticmethod
    def create_scenario_with_coverage(dimensions: List[str]) -> Dict[str, Any]:
        """Create scenario covering specific test dimensions."""
        scenario = TestHelpers.create_minimal_scenario()
        
        if "skill_matching" in dimensions:
            # Add providers with different skill levels
            scenario['providers'].extend([
                {
                    **TestHelpers.create_minimal_provider_data(),
                    'id': 'test-provider-002',
                    'name': 'Advanced Provider',
                    'skills': ['advanced_care', 'medication_management']
                },
                {
                    **TestHelpers.create_minimal_provider_data(),
                    'id': 'test-provider-003',
                    'name': 'Specialist Provider',
                    'skills': ['wound_care', 'iv_therapy']
                }
            ])
        
        if "availability" in dimensions:
            # Add provider with limited availability
            limited_provider = TestHelpers.create_minimal_provider_data()
            limited_provider['id'] = 'test-provider-limited'
            limited_provider['availability']['working_days'] = ['monday', 'wednesday']
            limited_provider['availability']['working_hours'] = ['10:00', '14:00']
            scenario['providers'].append(limited_provider)
        
        if "location" in dimensions:
            # Add consumers in different locations
            scenario['consumers'].extend([
                {
                    **TestHelpers.create_minimal_consumer_data(),
                    'id': 'test-consumer-002',
                    'name': 'Remote Consumer',
                    'location': {
                        'latitude': 40.6892, 'longitude': -74.0445,  # Brooklyn
                        'city': 'Brooklyn', 'state': 'NY'
                    }
                }
            ])
        
        return scenario
