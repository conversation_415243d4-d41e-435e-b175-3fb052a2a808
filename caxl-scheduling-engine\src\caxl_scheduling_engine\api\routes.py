"""
REST API routes for appointment scheduler.
"""

from datetime import date
from typing import List

from fastapi import APIRouter, HTTPException, Depends
from loguru import logger

from caxl_scheduling_engine.models import (
    PatientModel, CareStaffModel, AssignmentRequest, ReassignmentRequest,
    DayPlanRequest, AssignmentResult, LocationModel
)
from caxl_scheduling_engine.data.data_loader import DataLoader
from caxl_scheduling_engine.jobs.scheduler import AppointmentScheduler

router = APIRouter()


def get_data_loader() -> DataLoader:
    """Dependency to get DataLoader instance."""
    return DataLoader("data")


def get_scheduler() -> AppointmentScheduler:
    """Dependency to get AppointmentScheduler instance."""
    return AppointmentScheduler("config", daemon_mode=False)


@router.get("/patients", response_model=List[PatientModel])
async def get_patients(data_loader: DataLoader = Depends(get_data_loader)):
    """Load patients from data/consumers.yaml using data_loader.py"""
    try:
        consumers = data_loader.load_consumers()
        patients = []

        for consumer in consumers:
            # Handle location safely
            location_data = {
                "latitude": 0.0,
                "longitude": 0.0,
                "city": None,
                "state": None,
                "address": None
            }

            if consumer.location:
                location_data.update({
                    "latitude": consumer.location.latitude,
                    "longitude": consumer.location.longitude,
                    "city": consumer.location.city,
                    "state": consumer.location.state,
                    "address": consumer.location.address
                })

            # Handle preferences safely
            preferences_data = None
            if consumer.consumer_preferences:
                try:
                    preferences_data = consumer.consumer_preferences.__dict__
                except AttributeError:
                    preferences_data = None

            patient = PatientModel(
                id=str(consumer.id),
                name=consumer.name,
                location=LocationModel(**location_data),
                care_episode_id=consumer.care_episode_id,
                consumer_preferences=preferences_data
            )
            patients.append(patient)

        logger.info(f"Loaded {len(patients)} patients")
        return patients

    except Exception as e:
        logger.error(f"Failed to load patients: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to load patients: {str(e)}")


@router.get("/carestaff", response_model=List[CareStaffModel])
async def get_carestaff(data_loader: DataLoader = Depends(get_data_loader)):
    """Load care staff from data/providers.yaml using data_loader.py"""
    try:
        providers = data_loader.load_providers()
        carestaff = []
        
        for provider in providers:
            # Handle location safely
            location_data = {
                "latitude": 0.0,
                "longitude": 0.0,
                "city": None,
                "state": None,
                "address": None
            }

            if provider.home_location:
                location_data.update({
                    "latitude": provider.home_location.latitude,
                    "longitude": provider.home_location.longitude,
                    "city": provider.home_location.city,
                    "state": provider.home_location.state,
                    "address": provider.home_location.address
                })

            # Handle availability and capacity safely
            availability_data = None
            if provider.availability:
                try:
                    availability_data = provider.availability.__dict__
                except AttributeError:
                    availability_data = None

            capacity_data = None
            if provider.capacity:
                try:
                    capacity_data = provider.capacity.__dict__
                except AttributeError:
                    capacity_data = None

            staff = CareStaffModel(
                id=str(provider.id),
                name=provider.name,
                role=provider.role,
                skills=provider.skills,
                home_location=LocationModel(**location_data),
                availability=availability_data,
                capacity=capacity_data
            )
            carestaff.append(staff)
        
        logger.info(f"Loaded {len(carestaff)} care staff members")
        return carestaff
        
    except Exception as e:
        logger.error(f"Failed to load care staff: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to load care staff: {str(e)}")


@router.post("/assign-appointments", response_model=AssignmentResult)
async def assign_appointments(
    request: AssignmentRequest,
    scheduler: AppointmentScheduler = Depends(get_scheduler)
):
    """
    Accepts batch of appointments, triggers assignment solver.
    Ensures scheduler.py runs in daemon mode.
    """
    try:
        logger.info(f"Received assignment request for {len(request.appointments)} appointments")
        
        # Run assignment job
        # Note: Current scheduler doesn't support target_date/service_type parameters
        # In a full implementation, these would be passed to the underlying job
        result = scheduler.run_assign_appointments()
        
        # Convert result to API model
        summary = result.get('summary', {})
        assignment_result = AssignmentResult(
            success=True,
            message="Assignment completed successfully",
            total_appointments=summary.get('total_appointments', 0),
            assigned_appointments=summary.get('assigned_appointments', 0),
            unassigned_appointments=summary.get('unassigned_appointments', 0),
            processing_time_seconds=result.get('processing_time', 0.0),
            assignments=result.get('assignments', [])
        )
        
        logger.info(f"Assignment completed: {assignment_result.assigned_appointments}/{assignment_result.total_appointments} assigned")
        return assignment_result
        
    except Exception as e:
        logger.error(f"Assignment failed: {e}")
        raise HTTPException(status_code=500, detail=f"Assignment failed: {str(e)}")


@router.post("/reassign", response_model=AssignmentResult)
async def reassign_appointments(
    request: ReassignmentRequest,
    scheduler: AppointmentScheduler = Depends(get_scheduler)
):
    """
    Handles changes (e.g. provider unavailable, patient request) → unpin and reassign
    """
    try:
        logger.info(f"Received reassignment request for {len(request.appointment_ids)} appointments")
        logger.info(f"Reason: {request.reason}")
        
        # For now, trigger a full reassignment
        # In a real implementation, this would handle specific appointment unpinning
        result = scheduler.run_assign_appointments()
        
        # Convert result to API model
        summary = result.get('summary', {})
        assignment_result = AssignmentResult(
            success=True,
            message=f"Reassignment completed: {request.reason}",
            total_appointments=summary.get('total_appointments', 0),
            assigned_appointments=summary.get('assigned_appointments', 0),
            unassigned_appointments=summary.get('unassigned_appointments', 0),
            processing_time_seconds=result.get('processing_time', 0.0),
            assignments=result.get('assignments', [])
        )
        
        logger.info(f"Reassignment completed: {assignment_result.assigned_appointments}/{assignment_result.total_appointments} assigned")
        return assignment_result
        
    except Exception as e:
        logger.error(f"Reassignment failed: {e}")
        raise HTTPException(status_code=500, detail=f"Reassignment failed: {str(e)}")


@router.post("/run-dayplan", response_model=AssignmentResult)
async def run_dayplan(
    request: DayPlanRequest,
    scheduler: AppointmentScheduler = Depends(get_scheduler)
):
    """Runs dayplan solver for providers"""
    try:
        target_date = request.target_date or date.today()
        logger.info(f"Received dayplan request for {target_date}")
        
        # Run dayplan job
        result = scheduler.run_day_plan(target_date)
        
        # Convert result to API model
        assignment_result = AssignmentResult(
            success=True,
            message="Day plan completed successfully",
            total_appointments=result.total_appointments,
            assigned_appointments=result.assigned_appointments,
            unassigned_appointments=result.unassigned_appointments,
            processing_time_seconds=result.processing_time_seconds
        )
        
        logger.info(f"Day plan completed: {assignment_result.assigned_appointments}/{assignment_result.total_appointments} time slots assigned")
        return assignment_result
        
    except Exception as e:
        logger.error(f"Day plan failed: {e}")
        raise HTTPException(status_code=500, detail=f"Day plan failed: {str(e)}")
