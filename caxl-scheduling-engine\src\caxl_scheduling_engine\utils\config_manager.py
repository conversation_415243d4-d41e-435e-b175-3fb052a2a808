"""
Configuration management for healthcare scheduling optimization.

This module handles loading and validation of configuration files for the 2-stage optimization system:
1. Assignment Solver: Assigns providers and dates to appointments
2. Day Plan Solver: Optimizes timing and routing within a day
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional, List

import yaml
from loguru import logger

from caxl_scheduling_engine.model.domain import ServiceConfig, SchedulerConfig

logger = logging.getLogger(__name__)


class ConfigManager:
    """Manages configuration loading and validation for healthcare scheduling."""
    
    def __init__(self, config_folder: str = "caxl_scheduling_engine/config"):
        """Initialize the configuration manager."""
        # Find the config folder relative to the project root
        project_root = Path(__file__).parent.parent.parent
        self.config_folder = project_root / config_folder
        self.scheduler_config: Optional[SchedulerConfig] = None
        self.service_configs: Dict[str, ServiceConfig] = {}
        
        # Ensure config folder exists
        self.config_folder.mkdir(parents=True, exist_ok=True)
        
        # Load configurations
        self._load_scheduler_config()
        self._load_service_configs()
    
    def _load_scheduler_config(self) -> None:
        """Load the main scheduler configuration."""
        config_file = self.config_folder / "scheduler.yml"
        
        if not config_file.exists():
            raise FileNotFoundError(
                f"Scheduler configuration file not found: {config_file}\n"
                f"Please create the configuration file at {config_file} with required settings.\n"
                f"See the documentation for configuration format and examples."
            )
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            self.scheduler_config = SchedulerConfig(**config_data)
            logger.info(f"Loaded scheduler configuration from {config_file}")
            
        except Exception as e:
            raise RuntimeError(
                f"Failed to load scheduler configuration from {config_file}: {e}\n"
                f"Please check the configuration file format and ensure it's valid YAML."
            ) from e
    
    def _create_default_scheduler_config(self) -> None:
        """Create a default scheduler configuration file."""
        # This method has been removed - configurations must be provided externally
        pass
    
    def _load_service_configs(self) -> None:
        """Load service-specific configurations."""
        service_config_files = [
            "skilled_nursing.yml",
            "physical_therapy.yml",
            "personal_care.yml",
            "home_health.yml",
            "hospice.yml"
        ]
        
        for config_file in service_config_files:
            file_path = self.config_folder / config_file
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config_data = yaml.safe_load(f)
                    
                    service_config = ServiceConfig(**config_data)
                    service_type = service_config.service_type
                    self.service_configs[service_type] = service_config
                    logger.info(f"Loaded service config for {service_type}: {file_path}")
                    
                except Exception as e:
                    logger.error(f"Error loading service config {config_file}: {e}")
            else:
                logger.debug(f"Service config file not found: {file_path}")
        
        # Require at least one service configuration
        if not self.service_configs:
            raise FileNotFoundError(
                f"No service configuration files found in {self.config_folder}\n"
                f"Expected files: {', '.join(service_config_files)}\n"
                f"Please create at least one service configuration file with required settings.\n"
                f"See the documentation for configuration format and examples."
            )
    
    def _create_default_service_configs(self) -> None:
        """Create default service configuration files."""
        # This method has been removed - configurations must be provided externally
        pass
    
    def get_scheduler_config(self) -> SchedulerConfig:
        """Get the scheduler configuration."""
        if self.scheduler_config is None:
            raise RuntimeError(
                "Scheduler configuration not loaded. Please ensure the scheduler.yml file exists and is valid."
            )
        return self.scheduler_config
    
    def get_service_config(self, service_type: str) -> Optional[ServiceConfig]:
        """Get service configuration for a specific service type."""
        return self.service_configs.get(service_type)
    
    def get_all_service_configs(self) -> Dict[str, ServiceConfig]:
        """Get all service configurations."""
        return self.service_configs.copy()
    
    def get_service_types(self) -> List[str]:
        """Get list of available service types."""
        return list(self.service_configs.keys())
    
    def validate_configs(self) -> List[str]:
        """Validate all configurations and return list of issues."""
        issues = []
        
        # Validate scheduler config
        if not self.scheduler_config:
            issues.append("Scheduler configuration is missing")
        else:
            if self.scheduler_config.rolling_window_days <= 0:
                issues.append("rolling_window_days must be positive")
            if self.scheduler_config.batch_size <= 0:
                issues.append("batch_size must be positive")
            if self.scheduler_config.max_solving_time_seconds <= 0:
                issues.append("max_solving_time_seconds must be positive")
        
        # Validate service configs
        if not self.service_configs:
            issues.append("No service configurations found")
        else:
            for service_type, config in self.service_configs.items():
                if not config.required_skills:
                    issues.append(f"Service {service_type} has no required skills")
                if config.geographic_radius_miles <= 0:
                    issues.append(f"Service {service_type} has invalid geographic radius")
                if config.max_daily_appointments_per_provider <= 0:
                    issues.append(f"Service {service_type} has invalid max daily appointments")
        
        return issues

    def validate_configuration(self) -> Dict[str, Any]:
        """Validate the current configuration and return validation results."""
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "configs_loaded": {}
        }
        
        # Validate scheduler config
        if self.scheduler_config is None:
            validation_results["valid"] = False
            validation_results["errors"].append("Scheduler configuration not loaded")
        else:
            validation_results["configs_loaded"]["scheduler"] = True
            
            # Validate scheduler config values
            if self.scheduler_config.rolling_window_days <= 0:
                validation_results["errors"].append("rolling_window_days must be positive")
            
            if self.scheduler_config.batch_size <= 0:
                validation_results["errors"].append("batch_size must be positive")
            
            if self.scheduler_config.max_solving_time_seconds <= 0:
                validation_results["errors"].append("max_solving_time_seconds must be positive")
        
        # Validate service configs
        if not self.service_configs:
            validation_results["warnings"].append("No service configurations loaded")
        else:
            validation_results["configs_loaded"]["services"] = len(self.service_configs)
            
            for service_type, config in self.service_configs.items():
                # Validate service config values
                if config.geographic_radius_miles <= 0:
                    validation_results["errors"].append(f"{service_type}: geographic_radius_miles must be positive")
                
                if config.max_daily_appointments_per_provider <= 0:
                    validation_results["errors"].append(f"{service_type}: max_daily_appointments_per_provider must be positive")
                
                if config.max_weekly_hours_per_provider <= 0:
                    validation_results["errors"].append(f"{service_type}: max_weekly_hours_per_provider must be positive")
                
                if not config.required_skills:
                    validation_results["warnings"].append(f"{service_type}: no required skills specified")
                
                # Validate weight values
                weights = [
                    ("continuity_weight", config.continuity_weight),
                    ("workload_balance_weight", config.workload_balance_weight),
                    ("geographic_clustering_weight", config.geographic_clustering_weight),
                    ("patient_preference_weight", config.patient_preference_weight)
                ]
                
                for weight_name, weight_value in weights:
                    if weight_value < 0 or weight_value > 1:
                        validation_results["errors"].append(f"{service_type}: {weight_name} must be between 0 and 1")
                
                if config.capacity_threshold_percentage <= 0 or config.capacity_threshold_percentage > 1:
                    validation_results["errors"].append(f"{service_type}: capacity_threshold_percentage must be between 0 and 1")
        
        # Update overall validity
        if validation_results["errors"]:
            validation_results["valid"] = False
        
        return validation_results
    
    def reload_configuration(self) -> Dict[str, Any]:
        """Reload all configuration files."""
        logger.info("Reloading configuration files")
        
        # Clear existing configs
        self.scheduler_config = None
        self.service_configs.clear()
        
        # Reload configs
        self._load_scheduler_config()
        self._load_service_configs()
        
        # Validate and return results
        validation_results = self.validate_configuration()
        logger.info(f"Configuration reload complete. Valid: {validation_results['valid']}")
        
        return validation_results
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of the current configuration."""
        summary = {
            "config_folder": str(self.config_folder),
            "scheduler_config_loaded": self.scheduler_config is not None,
            "service_configs_count": len(self.service_configs),
            "service_types": self.get_service_types(),
            "validation": self.validate_configuration()
        }
        
        if self.scheduler_config:
            summary["scheduler_config"] = {
                "rolling_window_days": self.scheduler_config.rolling_window_days,
                "batch_size": self.scheduler_config.batch_size,
                "max_solving_time_seconds": self.scheduler_config.max_solving_time_seconds,
                "log_level": self.scheduler_config.log_level,
                
                # Core Feature Toggles (Basic Plan - Implemented)
                "enable_geographic_clustering": self.scheduler_config.enable_geographic_clustering,
                "enable_continuity_of_care": self.scheduler_config.enable_continuity_of_care,
                "enable_workload_balancing": self.scheduler_config.enable_workload_balancing,
                
                # Advanced Feature Toggles (Premium Plan - Implemented)
                "enable_patient_preferences": self.scheduler_config.enable_patient_preferences,
                "enable_provider_capacity_management": self.scheduler_config.enable_provider_capacity_management,
                "enable_healthcare_task_sequencing": self.scheduler_config.enable_healthcare_task_sequencing,
                "enable_travel_time_optimization": self.scheduler_config.enable_travel_time_optimization,
                "enable_break_time_management": self.scheduler_config.enable_break_time_management,
                "enable_route_optimization": self.scheduler_config.enable_route_optimization,
                
                # Performance and Monitoring Features
                "enable_metrics_collection": self.scheduler_config.enable_metrics_collection,
                "enable_detailed_logging": self.scheduler_config.enable_detailed_logging,
                "enable_constraint_tracking": self.scheduler_config.enable_constraint_tracking,
                
                # Notification Features
                "enable_email_notifications": self.scheduler_config.enable_email_notifications,
                "enable_slack_notifications": self.scheduler_config.enable_slack_notifications,
                "enable_sms_notifications": self.scheduler_config.enable_sms_notifications,
                
                # Integration Features
                "enable_api_access": self.scheduler_config.enable_api_access,
                "enable_webhook_integrations": self.scheduler_config.enable_webhook_integrations,
                "enable_database_persistence": self.scheduler_config.enable_database_persistence,
            }
        
        summary["service_configs"] = {}
        for service_type, config in self.service_configs.items():
            summary["service_configs"][service_type] = {
                "required_skills": config.required_skills,
                "geographic_radius_miles": config.geographic_radius_miles,
                "max_daily_appointments_per_provider": config.max_daily_appointments_per_provider,
                "continuity_weight": config.continuity_weight,
                "workload_balance_weight": config.workload_balance_weight,
                "geographic_clustering_weight": config.geographic_clustering_weight,
                "patient_preference_weight": config.patient_preference_weight
            }
        
        return summary 