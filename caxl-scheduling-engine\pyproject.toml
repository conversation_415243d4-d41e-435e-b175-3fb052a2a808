[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "caxl-scheduling-engine"
version = "0.1.0"
description = "US-based appointment scheduling system with Timefold optimization"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "timefold>=1.23.0b0",
    "pydantic>=2.0.0",
    "loguru>=0.7.0",
    "pyyaml>=6.0",
    "schedule>=1.2.0",
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]
api = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "httpx>=0.25.0",  # For testing
]

[project.scripts]
assign-appointments = "caxl_scheduling_engine.jobs.assign_appointments:main"
day-plan = "caxl_scheduling_engine.jobs.day_plan:main"
scheduler = "caxl_scheduling_engine.scheduler:main"
api-server = "caxl_scheduling_engine.api.app:main"
test-scenarios = "tests.test_runner:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--cov=caxl_scheduling_engine --cov-report=html --cov-report=term" 