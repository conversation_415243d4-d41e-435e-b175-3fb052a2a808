# CAXL Scheduling Engine Documentation v1 - Summary

## Excel File Created: `CAXL_Scheduling_Engine_Documentation_v1.xlsx`

### Tab 1: Domain Model (Hierarchical Structure)

**Provider Entity & Related Classes:**
- **Provider**: Core provider fields (id, name, home_location, service_areas, languages, transportation, etc.)
- **ProviderAvailability**: Availability schedule fields (primary_shift, working_days, break_periods, holidays, etc.)
- **ProviderCapacity**: Capacity constraints (max_allocated_task_points_in_day, max_tasks_count_in_day, etc.)
- **ProviderPreferences**: Provider preferences (blacklisted_consumers, preferred_task_types, etc.)

**Consumer Entity & Related Classes:**
- **Consumer**: Core consumer fields (id, name, location, care_episode_id, etc.)
- **ConsumerPreferences**: Consumer preferences (preferred_days, preferred_hours, cultural_considerations, etc.)

**Appointment Entity & Related Classes:**
- **AppointmentData**: Core appointment fields (id, consumer_id, appointment_date, required_skills, duration_min, etc.)
- **AppointmentTiming**: Timing constraints (is_timed_visit, preferred_time, time_flexibility_minutes, etc.)
- **AppointmentRelationships**: Relationships and dependencies (care_episode_id, prerequisite_appointment_ids, etc.)
- **AppointmentPinning**: Pinning management (is_pinned, pin_provider, pin_date, pin_time, etc.)

### Tab 2: Constraints (c001-c016)

**Assignment Stage Constraints:**
- c001: Provider Skill Validation
- c002: Date-based Availability
- c003: Geographic Service Area
- c004: Time-based Availability & Timed Visit Date Assignment
- c005: Workload Balance Optimization
- c006: Geographic Clustering Optimization
- c007: Patient Preference Matching
- c008: Provider Capacity Management
- c009: Continuity of Care Optimization

**Scheduling/Day Planning Stage Constraints:**
- c010: Timeslot Availability Validation
- c011: Appointment Overlap Prevention
- c012: Flexible Appointment Timing Optimization
- c013: Healthcare Task Sequencing
- c014: Route Travel Time Optimization
- c015: Timed Appointment Pinning
- c016: Route Optimization

### Tab 3: Global Configuration

**Core Configuration:**
- rolling_window_days, max_solving_time_seconds, config_folder, log_level

**Feature Toggles:**
- enable_geographic_clustering, enable_continuity_of_care, enable_workload_balancing
- enable_patient_preferences, enable_provider_capacity_management
- enable_healthcare_task_sequencing, enable_travel_time_optimization
- enable_break_time_management, enable_route_optimization

**Advanced Traffic Integration:**
- enable_advanced_traffic_integration
- Traffic API configurations (Google Maps, Weather APIs)
- Traffic model settings (urban cities, speed factors, time adjustments)

### Tab 4: Service Configuration & Mapping

**Service-Level Parameters:**
- service_type, required_skills, geographic_radius_miles
- max_daily_appointments_per_provider, max_weekly_hours_per_provider
- continuity_weight, workload_balance_weight, geographic_clustering_weight
- patient_preference_weight, capacity_threshold_percentage

**Constraint Mapping:**
- Each parameter mapped to the constraints that use it
- Clear indication of whether parameter is Global or Service level

## Document Features

### Formatting & Style
- **Headers**: Blue background (#366092) with white bold text
- **Borders**: Thin borders around all cells
- **Column Widths**: Auto-adjusted for optimal readability
- **Alignment**: Centered headers, left-aligned content

### Data Organization
- **Hierarchical Structure**: Related fields grouped under main entities
- **Constraint Functions**: Only direct implementation functions included
- **Configuration Mapping**: Clear mapping between config parameters and constraints
- **Examples**: Practical examples for each field and parameter

### File Information
- **Size**: 15KB
- **Format**: Excel (.xlsx)
- **Version**: v1
- **Generated**: Using openpyxl library
- **Source**: Based on actual code analysis from caxl-scheduling-engine

## Usage

This documentation serves as a comprehensive reference for:
1. **Developers**: Understanding the domain model structure
2. **System Administrators**: Configuring the scheduling system
3. **Business Analysts**: Understanding constraint logic and configuration options
4. **QA Teams**: Understanding what each constraint validates

The document maintains the same structure and formatting as the original reference document while being specifically tailored to the CAXL Scheduling Engine implementation. 