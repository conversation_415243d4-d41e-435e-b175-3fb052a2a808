"""
Timefold-specific planning entities and solutions.

This module contains the Timefold/OptaPy decorators and annotations for the optimization solver.
The domain models in domain.py remain framework-agnostic.
"""

from dataclasses import dataclass, field
from datetime import date, time
from typing import List, Optional, Annotated

from timefold.solver.domain import (
    PlanningId, PlanningVariable, PlanningEntityCollectionProperty,
    ProblemFactCollectionProperty, ValueRangeProvider, PlanningScore,
    planning_entity, planning_solution
)
from timefold.solver.score import HardSoftScore

from .domain import (
    AppointmentData, Provider, Consumer, Location,
    AppointmentTiming, AppointmentRelationships, AppointmentPinning,
    ServiceConfig, SchedulerConfig, BatchAssignmentResult, AssignmentResult
)


# Timefold-specific planning entities
@planning_entity
@dataclass
class AppointmentAssignment:
    """Planning entity representing the assignment of an appointment to a provider and date."""
    id: Annotated[str, PlanningId]
    appointment_data: AppointmentData
    provider: Annotated[Optional[Provider], PlanningVariable] = field(default=None)
    assigned_date: Annotated[Optional[date], PlanningVariable] = field(default=None)
    
    def __str__(self):
        if self.provider is None or self.assigned_date is None:
            return f"{self.appointment_data.id} -> unassigned"
        return f"{self.appointment_data.id} -> {self.provider.name} on {self.assigned_date}"


@planning_entity
@dataclass
class TimeSlotAssignment:
    """Planning entity for assigning time slots to scheduled appointments."""
    id: Annotated[str, PlanningId]
    scheduled_appointment: 'ScheduledAppointment'  # Use planning version
    time_slot: Annotated[Optional[time], PlanningVariable] = field(default=None)
    
    def __str__(self):
        if self.time_slot is None:
            return f"{self.scheduled_appointment} -> no time assigned"
        return f"{self.scheduled_appointment.appointment_data.id} -> {self.scheduled_appointment.provider.name} at {self.time_slot}"


@dataclass
class ScheduledAppointment:
    """Planning version of scheduled appointment for day planning."""
    id: str
    appointment_data: AppointmentData
    provider: Provider
    assigned_date: date
    assigned_time: Optional[time] = None  # Will be assigned by DayPlan job
    
    def __str__(self):
        time_str = f" at {self.assigned_time}" if self.assigned_time else " (time TBD)"
        return f"{self.appointment_data.id} -> {self.provider.name} on {self.assigned_date}{time_str}"


# Timefold-specific planning solutions
@planning_solution
@dataclass
class AppointmentSchedule:
    """Solution representing the complete appointment schedule."""
    id: str
    providers: Annotated[List[Provider],
                         ProblemFactCollectionProperty,
                         ValueRangeProvider]
    available_dates: Annotated[List[date],
                               ProblemFactCollectionProperty,
                               ValueRangeProvider]
    appointment_assignments: Annotated[List[AppointmentAssignment],
                                      PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)
    
    def get_provider_assignments(self, provider: Provider) -> List[AppointmentAssignment]:
        """Get all appointments assigned to a specific provider."""
        return [assignment for assignment in self.appointment_assignments 
                if assignment.provider == provider]
    
    def get_provider_daily_workload(self, provider: Provider, target_date: date) -> int:
        """Calculate provider's workload for a specific date."""
        assignments = self.get_provider_assignments(provider)
        return len([a for a in assignments if a.assigned_date == target_date])


@planning_solution
@dataclass
class DaySchedule:
    """Solution representing the daily time slot assignments."""
    id: str
    date: date
    time_slots: Annotated[List[time],
                          ProblemFactCollectionProperty,
                          ValueRangeProvider]
    scheduled_appointments: Annotated[List[ScheduledAppointment],
                                      ProblemFactCollectionProperty]
    time_assignments: Annotated[List[TimeSlotAssignment],
                                PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)


# Utility functions for converting between domain and planning models
def create_planning_assignment(domain_assignment) -> AppointmentAssignment:
    """Convert domain assignment to planning assignment."""
    from .domain import AppointmentAssignment as DomainAppointmentAssignment
    return AppointmentAssignment(
        id=domain_assignment.id,
        appointment_data=domain_assignment.appointment_data,
        provider=domain_assignment.provider,
        assigned_date=domain_assignment.assigned_date
    )


def create_planning_schedule(domain_schedule) -> AppointmentSchedule:
    """Convert domain schedule to planning schedule."""
    from .domain import AppointmentSchedule as DomainAppointmentSchedule
    planning_assignments = [
        create_planning_assignment(assignment) 
        for assignment in domain_schedule.appointment_assignments
    ]
    
    return AppointmentSchedule(
        id=domain_schedule.id,
        providers=domain_schedule.providers,
        available_dates=domain_schedule.available_dates,
        appointment_assignments=planning_assignments,
        score=domain_schedule.score
    )


def create_domain_assignment(planning_assignment: AppointmentAssignment):
    """Convert planning assignment to domain assignment."""
    from .domain import AppointmentAssignment as DomainAppointmentAssignment
    return DomainAppointmentAssignment(
        id=planning_assignment.id,
        appointment_data=planning_assignment.appointment_data,
        provider=planning_assignment.provider,
        assigned_date=planning_assignment.assigned_date
    )


def create_domain_schedule(planning_schedule: AppointmentSchedule):
    """Convert planning schedule to domain schedule."""
    from .domain import AppointmentSchedule as DomainAppointmentSchedule
    domain_assignments = [
        create_domain_assignment(assignment) 
        for assignment in planning_schedule.appointment_assignments
    ]
    
    return DomainAppointmentSchedule(
        id=planning_schedule.id,
        providers=planning_schedule.providers,
        available_dates=planning_schedule.available_dates,
        appointment_assignments=domain_assignments,
        score=planning_schedule.score
    ) 