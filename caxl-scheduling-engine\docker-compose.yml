services:
  caxl_scheduling_engine:
    build:
      context: .
      dockerfile: Dockerfile.dev
    network_mode: host
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=development
      - DEBUG=True
      - HOST=0.0.0.0
      - PORT=8080
      - PROJECT_NAME=caxl-scheduling-engine
      - API_V1_STR=/api/v1
      - CORS_ORIGINS=["*"]
      - CORS_ALLOW_CREDENTIALS=True
      - CORS_ALLOW_METHODS=["*"]
      - CORS_ALLOW_HEADERS=["*"]
      - CORS_EXPOSE_HEADERS=["*"]
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
    volumes:
      - .:/app
    command: uvicorn src.main:app --host 0.0.0.0 --port 8080 --reload
